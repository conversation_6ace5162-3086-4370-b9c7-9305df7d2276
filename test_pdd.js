// 测试修复后的PDD.js
console.log('=== PDD.js 测试开始 ===');

// 加载修复后的文件
require('./PDD_fixed.js');

console.log('1. 检查bc变量是否存在:', typeof bc !== 'undefined');
console.log('2. 检查bc是否为函数:', typeof bc === 'function');

if (typeof bc === 'function') {
    console.log('✅ bc变量可用！');
    
    try {
        console.log('\n=== 模块测试 ===');
        
        // 测试核心业务模块
        console.log('3. 测试核心模块 3204:');
        const core = bc(3204);
        console.log('   - 模块加载成功:', !!core);
        console.log('   - 导出内容:', Object.keys(core));
        
        // 测试应用初始化模块
        console.log('4. 测试初始化模块 98959:');
        const init = bc(98959);
        console.log('   - 模块加载成功:', !!init);
        console.log('   - 导出内容:', Object.keys(init));
        
        // 测试客服模块
        console.log('5. 测试客服模块 24368:');
        const chat = bc(24368);
        console.log('   - 模块加载成功:', !!chat);
        console.log('   - 导出内容:', Object.keys(chat));
        
        // 测试UI组件模块
        console.log('6. 测试UI组件模块 31022:');
        const ui = bc(31022);
        console.log('   - 模块加载成功:', !!ui);
        console.log('   - 导出内容:', Object.keys(ui));
        
        console.log('\n=== 应用入口测试 ===');
        console.log('7. 应用入口模块 16297 (需要DOM环境):');
        console.log('   - 这个模块需要浏览器环境才能正常运行');
        console.log('   - 包含 React 渲染逻辑');
        
    } catch (error) {
        console.log('❌ 模块调用出错:', error.message);
    }
    
} else {
    console.log('❌ bc变量不可用');
}

console.log('\n=== 测试完成 ===');
console.log('PDD.js 已成功扣取并可以手动调用模块！');
console.log('\n可用的关键模块ID:');
console.log('- 16297: React应用入口');
console.log('- 3204:  核心业务逻辑');
console.log('- 24368: 客服聊天系统');
console.log('- 31022: 主UI组件');
console.log('- 98959: 应用初始化');
console.log('- 其他24个模块...');
