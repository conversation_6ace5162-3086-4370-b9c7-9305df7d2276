import requests
import execjs
import time




class RedMan:
    def __init__(self):
        self.url  = "https://ucp.hrdjyun.com:60359/api/dy"
        self.headers = {
            #"Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
        }
        with open("红人.js", "r", encoding="utf-8") as f:
            js_code = f.read()
            self.ctx = execjs.compile(js_code)
        self.params = {
            "no":"dy00022",
            "data":{
                "days":1,
                "rankType":5,
                "liveDay":"2024-01-06"
            }
        }

    def get_data(self):
        req = requests.post(self.url, headers=self.headers, json=self.params)
        print(req.json())