# PDD.js Webpack扣取转换指南

## 🎯 转换目标
将PDD.js从自执行的webpack包转换为可手动调用的形式，像医保.js一样。

## 📋 具体修改步骤

### 第1步：添加环境适配
在文件最开头添加：
```javascript
window = global

var bc;
```

### 第2步：修改IIFE开始
找到第1行：
```javascript
( () => {
```
改为：
```javascript
!function (e) {
```

### 第3步：修改变量声明
找到第2行：
```javascript
var e, t, n, r, o, a = {
```
改为：
```javascript
var t, n, r, o, a = {}, i = {};
```

### 第4步：移动模块定义
将从第2行开始的整个模块对象：
```javascript
a = {
    3204: (e, t, n) => { ... },
    3376: (e, t, n) => { ... },
    // ... 所有模块到第5247行的 }
}
```
这部分需要剪切出来，稍后放到函数调用的参数位置。

### 第5步：保留webpack运行时
保留从第5248行开始的webpack运行时代码：
```javascript
}, i = {};
function l(e) {
    // webpack require函数
}
l.m = a,
// ... 其他webpack方法
```

### 第6步：注释自动执行
找到文件末尾的自动执行部分（大约5615-5619行）：
```javascript
l.O(void 0, [845], () => l(42161)),
l.O(void 0, [845], () => l(70113)),
l.O(void 0, [845], () => l(86918));
var c = l.O(void 0, [845], () => l(16297));
c = l.O(c)
```
将这些行注释掉：
```javascript
// l.O(void 0, [845], () => l(42161)),
// l.O(void 0, [845], () => l(70113)),
// l.O(void 0, [845], () => l(86918));
// var c = l.O(void 0, [845], () => l(16297));
// c = l.O(c)
```

### 第7步：添加bc赋值
在注释掉的自动执行代码后面添加：
```javascript
bc = l;
```

### 第8步：修改函数结尾
将最后的：
```javascript
}
)();
```
改为：
```javascript
}({
    // 这里放入第4步剪切的所有模块定义
    3204: (e, t, n) => { ... },
    3376: (e, t, n) => { ... },
    // ... 所有模块
});
```

## 🔍 关键模块ID
- **16297**: 应用入口模块
- **3204**: 核心业务逻辑
- **24368**: 客服聊天功能
- **31022**: 主UI组件

## ✅ 验证方法
转换完成后测试：
```javascript
require('./PDD_extracted.js');
console.log(typeof bc);  // 应该输出 "function"
bc(16297);  // 启动应用
bc(3204);   // 获取核心功能
```

## ⚠️ 注意事项
1. 确保所有大括号匹配
2. 模块定义不要遗漏
3. webpack运行时代码要完整
4. 变量作用域要正确

## 🛠️ 简化方法
如果手动修改太复杂，可以：
1. 复制医保.js的webpack运行时部分
2. 只替换模块定义部分
3. 调整变量名匹配
