!function(e) {
    var e, t, n, r, o, a = {}, i = {};
    function l(e) {
        var t = i[e];
        if (void 0 !== t)
            return t.exports;
        var n = i[e] = {
            id: e,
            loaded: !1,
            exports: {}
        };
        return a[e].call(n.exports, n, n.exports, l),
        n.loaded = !0,
        n.exports
    }
    l.m = a,
    l.amdD = function() {
        throw new Error("define cannot be used indirect")
    }
    ,
    e = [],
    l.O = (t, n, r, o) => {
        if (!n) {
            var a = 1 / 0;
            for (s = 0; s < e.length; s++) {
                for (var [n,r,o] = e[s], i = !0, c = 0; c < n.length; c++)
                    (!1 & o || a >= o) && Object.keys(l.O).every(e => l.O[e](n[c])) ? n.splice(c--, 1) : (i = !1,
                    o < a && (a = o));
                if (i) {
                    e.splice(s--, 1);
                    var u = r();
                    void 0 !== u && (t = u)
                }
            }
            return t
        }
        o = o || 0;
        for (var s = e.length; s > 0 && e[s - 1][2] > o; s--)
            e[s] = e[s - 1];
        e[s] = [n, r, o]
    }
    ,
    l.n = e => {
        var t = e && e.__esModule ? () => e.default : () => e;
        return l.d(t, {
            a: t
        }),
        t
    }
    ,
    n = Object.getPrototypeOf ? e => Object.getPrototypeOf(e) : e => e.__proto__,
    l.t = function(e, r) {
        if (1 & r && (e = this(e)),
        8 & r)
            return e;
        if ("object" == typeof e && e) {
            if (4 & r && e.__esModule)
                return e;
            if (16 & r && "function" == typeof e.then)
                return e
        }
        var o = Object.create(null);
        l.r(o);
        var a = {};
        t = t || [null, n({}), n([]), n(n)];
        for (var i = 2 & r && e; "object" == typeof i && !~t.indexOf(i); i = n(i))
            Object.getOwnPropertyNames(i).forEach(t => a[t] = () => e[t]);
        return a.default = () => e,
        l.d(o, a),
        o
    }
    ,
    l.d = (e, t) => {
        for (var n in t)
            l.o(t, n) && !l.o(e, n) && Object.defineProperty(e, n, {
                enumerable: !0,
                get: t[n]
            })
    }
    ,
    l.f = {},
    l.e = e => Promise.all(Object.keys(l.f).reduce( (t, n) => (l.f[n](e, t),
    t), [])),
    l.u = e => (({
        28: "relate-authorization",
        44: "home-main",
        51: "login-hosting",
        77: "graph",
        98: "main-message",
        113: "enterprise-entry",
        173: "rule-center",
        221: "order-detail",
        232: "activity-login",
        242: "seller-login",
        244: "modify-mobile",
        286: "qualification",
        300: "entity-relate",
        396: "login-mms",
        450: "reset-password",
        499: "mall-transfer",
        536: "login-register",
        662: "order-list",
        683: "site-trusteeship",
        966: "login"
    }[e] || e) + "." + {
        28: "277b8641",
        44: "fb848bff",
        51: "228b66f8",
        77: "777c5d6b",
        91: "2bb740c3",
        93: "ff2967f5",
        98: "323f2bb3",
        113: "505597c3",
        125: "3c84177b",
        173: "366bb4ca",
        179: "0c5cc8ea",
        194: "b3c8ced5",
        218: "4aa5b8d0",
        221: "1a0d58b8",
        232: "5f91be51",
        242: "0d4dda4a",
        244: "67f4adf6",
        286: "284e265a",
        300: "cd5e7698",
        309: "aee81348",
        372: "53ad0d70",
        379: "49072434",
        396: "233ecf54",
        424: "3c9f7a12",
        450: "e416df8f",
        499: "100efbcd",
        536: "8f5c8796",
        543: "b8bed232",
        605: "b99e3ae2",
        612: "388bb65c",
        662: "21a4f76e",
        674: "28fc640d",
        683: "a969396f",
        879: "00003dfd",
        940: "59b940bc",
        966: "48c58523"
    }[e] + ".chunk.js"),
    l.miniCssF = e => e + "." + {
        28: "7d07953d64c70ea072ac",
        44: "e57d3af12f03f2bebd78",
        51: "f81ec338d5427c09fbe2",
        98: "77a104ccf41a7718f3bf",
        113: "8dd14113fa46d5b28a2e",
        173: "934f35e7872bb4469d75",
        179: "ee48249ddad7094c2ddf",
        232: "6bebe2e4c03f066be14a",
        242: "edb957b134c0f6f11575",
        244: "c59d7643c766a10e8f65",
        300: "1f61e5ab74f41fe7abe4",
        372: "261ed83703a51c4d4824",
        396: "51a98613c1a263d0e7ce",
        450: "96e61c211569c8f88c73",
        499: "7d03d8ac670bc74ba865",
        536: "49681bc8fe210e47e438",
        683: "6b565930310ab8ce9cc2",
        966: "f1083f67772367fb9bb7"
    }[e] + ".css",
    l.g = function() {
        if ("object" == typeof globalThis)
            return globalThis;
        try {
            return this || new Function("return this")()
        } catch (e) {
            if ("object" == typeof window)
                return window
        }
    }(),
    l.hmd = e => ((e = Object.create(e)).children || (e.children = []),
    Object.defineProperty(e, "exports", {
        enumerable: !0,
        set: () => {
            throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: " + e.id)
        }
    }),
    e),
    l.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t),
    r = {},
    o = "bgb-sc-settle:",
    l.l = (e, t, n, a) => {
        if (r[e])
            r[e].push(t);
        else {
            var i, c;
            if (void 0 !== n)
                for (var u = document.getElementsByTagName("script"), s = 0; s < u.length; s++) {
                    var d = u[s];
                    if (d.getAttribute("src") == e || d.getAttribute("data-webpack") == o + n) {
                        i = d;
                        break
                    }
                }
            i || (c = !0,
            (i = document.createElement("script")).charset = "utf-8",
            i.timeout = 120,
            l.nc && i.setAttribute("nonce", l.nc),
            i.setAttribute("data-webpack", o + n),
            i.src = e),
            r[e] = [t];
            var f = (t, n) => {
                i.onerror = i.onload = null,
                clearTimeout(p);
                var o = r[e];
                if (delete r[e],
                i.parentNode && i.parentNode.removeChild(i),
                o && o.forEach(e => e(n)),
                t)
                    return t(n)
            }
              , p = setTimeout(f.bind(null, void 0, {
                type: "timeout",
                target: i
            }), 12e4);
            i.onerror = f.bind(null, i.onerror),
            i.onload = f.bind(null, i.onload),
            c && document.head.appendChild(i)
        }
    }
    ,
    l.r = e => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
            value: "Module"
        }),
        Object.defineProperty(e, "__esModule", {
            value: !0
        })
    }
    ,
    l.nmd = e => (e.paths = [],
    e.children || (e.children = []),
    e),
    l.p = "https://bstatic.cdnfe.com/static/bgb-sc-settle/",
    ( () => {
        if ("undefined" != typeof document) {
            var e = {
                792: 0
            };
            l.f.miniCss = (t, n) => {
                e[t] ? n.push(e[t]) : 0 !== e[t] && {
                    28: 1,
                    44: 1,
                    51: 1,
                    98: 1,
                    113: 1,
                    173: 1,
                    179: 1,
                    232: 1,
                    242: 1,
                    244: 1,
                    300: 1,
                    372: 1,
                    396: 1,
                    450: 1,
                    499: 1,
                    536: 1,
                    683: 1,
                    966: 1
                }[t] && n.push(e[t] = (e => new Promise( (t, n) => {
                    var r = l.miniCssF(e)
                      , o = l.p + r;
                    if (( (e, t) => {
                        for (var n = document.getElementsByTagName("link"), r = 0; r < n.length; r++) {
                            var o = (i = n[r]).getAttribute("data-href") || i.getAttribute("href");
                            if ("stylesheet" === i.rel && (o === e || o === t))
                                return i
                        }
                        var a = document.getElementsByTagName("style");
                        for (r = 0; r < a.length; r++) {
                            var i;
                            if ((o = (i = a[r]).getAttribute("data-href")) === e || o === t)
                                return i
                        }
                    }
                    )(r, o))
                        return t();
                    ( (e, t, n, r, o) => {
                        var a = document.createElement("link");
                        a.rel = "stylesheet",
                        a.type = "text/css",
                        l.nc && (a.nonce = l.nc),
                        a.onerror = a.onload = n => {
                            if (a.onerror = a.onload = null,
                            "load" === n.type)
                                r();
                            else {
                                var i = n && n.type
                                  , l = n && n.target && n.target.href || t
                                  , c = new Error("Loading CSS chunk " + e + " failed.\n(" + i + ": " + l + ")");
                                c.name = "ChunkLoadError",
                                c.code = "CSS_CHUNK_LOAD_FAILED",
                                c.type = i,
                                c.request = l,
                                a.parentNode && a.parentNode.removeChild(a),
                                o(c)
                            }
                        }
                        ,
                        a.href = t,
                        document.head.appendChild(a)
                    }
                    )(e, o, 0, t, n)
                }
                ))(t).then( () => {
                    e[t] = 0
                }
                , n => {
                    throw delete e[t],
                    n
                }
                ))
            }
        }
    }
    )(),
    ( () => {
        var e = {
            792: 0
        };
        l.f.j = (t, n) => {
            var r = l.o(e, t) ? e[t] : void 0;
            if (0 !== r)
                if (r)
                    n.push(r[2]);
                else {
                    var o = new Promise( (n, o) => r = e[t] = [n, o]);
                    n.push(r[2] = o);
                    var a = l.p + l.u(t)
                      , i = new Error;
                    l.l(a, n => {
                        if (l.o(e, t) && (0 !== (r = e[t]) && (e[t] = void 0),
                        r)) {
                            var o = n && ("load" === n.type ? "missing" : n.type)
                              , a = n && n.target && n.target.src;
                            i.message = "Loading chunk " + t + " failed.\n(" + o + ": " + a + ")",
                            i.name = "ChunkLoadError",
                            i.type = o,
                            i.request = a,
                            r[1](i)
                        }
                    }
                    , "chunk-" + t, t)
                }
        }
        ,
        l.O.j = t => 0 === e[t];
        var t = (t, n) => {
            var r, o, [a,i,c] = n, u = 0;
            if (a.some(t => 0 !== e[t])) {
                for (r in i)
                    l.o(i, r) && (l.m[r] = i[r]);
                if (c)
                    var s = c(l)
            }
            for (t && t(n); u < a.length; u++)
                o = a[u],
                l.o(e, o) && e[o] && e[o][0](),
                e[o] = 0;
            return l.O(s)
        }
          , n = self.webpackChunkbgb_sc_settle = self.webpackChunkbgb_sc_settle || [];
        n.forEach(t.bind(null, 0)),
        n.push = t.bind(null, n.push.bind(n))
    }
    )()
    // l.O(void 0, [845], () => l(42161)),
    // l.O(void 0, [845], () => l(70113)),
    // l.O(void 0, [845], () => l(86918));
    // var c = l.O(void 0, [845], () => l(16297));
    // c = l.O(c)
}
({
        3204: (e, t, n) => {
            "use strict";
            n.d(t, {
                IM: () => C,
                Jt: () => O,
                bE: () => N,
                rZ: () => E
            });
            var r = n(25473)
              , o = n(85803)
              , a = n(53355)
              , i = n(12539)
              , l = n(21755)
              , c = n(57653)
              , u = n(49155)
              , s = n(31784)
              , d = n(3376)
              , f = function() {
                return f = Object.assign || function(e) {
                    for (var t, n = 1, r = arguments.length; n < r; n++)
                        for (var o in t = arguments[n])
                            Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
                    return e
                }
                ,
                f.apply(this, arguments)
            }
              , p = function(e, t, n, r) {
                return new (n || (n = Promise))(function(o, a) {
                    function i(e) {
                        try {
                            c(r.next(e))
                        } catch (e) {
                            a(e)
                        }
                    }
                    function l(e) {
                        try {
                            c(r.throw(e))
                        } catch (e) {
                            a(e)
                        }
                    }
                    function c(e) {
                        var t;
                        e.done ? o(e.value) : (t = e.value,
                        t instanceof n ? t : new n(function(e) {
                            e(t)
                        }
                        )).then(i, l)
                    }
                    c((r = r.apply(e, t || [])).next())
                }
                )
            }
              , v = function(e, t) {
                var n, r, o, a = {
                    label: 0,
                    sent: function() {
                        if (1 & o[0])
                            throw o[1];
                        return o[1]
                    },
                    trys: [],
                    ops: []
                }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                return i.next = l(0),
                i.throw = l(1),
                i.return = l(2),
                "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                    return this
                }
                ),
                i;
                function l(l) {
                    return function(c) {
                        return function(l) {
                            if (n)
                                throw new TypeError("Generator is already executing.");
                            for (; i && (i = 0,
                            l[0] && (a = 0)),
                            a; )
                                try {
                                    if (n = 1,
                                    r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                    0) : r.next) && !(o = o.call(r, l[1])).done)
                                        return o;
                                    switch (r = 0,
                                    o && (l = [2 & l[0], o.value]),
                                    l[0]) {
                                    case 0:
                                    case 1:
                                        o = l;
                                        break;
                                    case 4:
                                        return a.label++,
                                        {
                                            value: l[1],
                                            done: !1
                                        };
                                    case 5:
                                        a.label++,
                                        r = l[1],
                                        l = [0];
                                        continue;
                                    case 7:
                                        l = a.ops.pop(),
                                        a.trys.pop();
                                        continue;
                                    default:
                                        if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                            a = 0;
                                            continue
                                        }
                                        if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                            a.label = l[1];
                                            break
                                        }
                                        if (6 === l[0] && a.label < o[1]) {
                                            a.label = o[1],
                                            o = l;
                                            break
                                        }
                                        if (o && a.label < o[2]) {
                                            a.label = o[2],
                                            a.ops.push(l);
                                            break
                                        }
                                        o[2] && a.ops.pop(),
                                        a.trys.pop();
                                        continue
                                    }
                                    l = t.call(e, a)
                                } catch (e) {
                                    l = [6, e],
                                    r = 0
                                } finally {
                                    n = o = 0
                                }
                            if (5 & l[0])
                                throw l[1];
                            return {
                                value: l[0] ? l[1] : void 0,
                                done: !0
                            }
                        }([l, c])
                    }
                }
            }
              , m = (0,
            s.$z)();
            function h() {
                if (!["/login", "/login/register", "/settle/seller-login"].includes(location.pathname)) {
                    var e = "".concat(location.protocol, "//").concat(window.location.host, "/login?redirectUrl=").concat(encodeURIComponent(window.location.href));
                    location.href = e
                }
            }
            var y = function(e, t, n, r) {
                var o = e.res
                  , a = e.data;
                if (!o)
                    return n(a);
                if (Number(o.status) >= 200 && Number(o.status) < 400)
                    return t(e);
                var i = (0,
                u.Zo)(a, o);
                (403 === o.status && 40001 === (null == a ? void 0 : a.errorCode) || "opaqueredirect" === o.type) && (null == r || r()),
                n(i)
            }
              , g = (0,
            c.Ay)({
                redirectToLogin: h,
                replaceStatusHandler: function(e, t, n) {
                    return y(e, t, n, h)
                }
            })
              , b = (0,
            c.Ay)({
                redirectToLogin: h,
                replaceStatusHandler: function(e, t, n) {
                    return y(e, t, n)
                }
            })
              , _ = (0,
            r.hn)(l.A, a.Ay, i.A, o.A, b);
            function E(e) {
                return p(this, arguments, void 0, function(e, t, n) {
                    var r, o;
                    return void 0 === t && (t = {}),
                    void 0 === n && (n = {}),
                    v(this, function(a) {
                        return r = f(f(f({}, w), k), {
                            headers: f(f({}, I), null == n ? void 0 : n.headers)
                        }),
                        o = JSON.stringify(t || {}),
                        (0,
                        u.$A)(n) && (delete r.headers[j],
                        o = t),
                        [2, _((0,
                        u.oV)(m, e), f(f(f({}, r), {
                            body: o
                        }), n)).then(function(e) {
                            return (0,
                            u.Y4)(e, n)
                        })]
                    })
                })
            }
            var j = u.AA.CONTENT_TYPE_IDENTIFIER
              , I = u.AA.HEADERS_INIT
              , w = u.AA.POST_REQUEST_INIT
              , k = {
                mode: "cors",
                credentials: "include",
                cache: "no-cache"
            }
              , S = (0,
            r.hn)(l.A, a.Ay, i.A, o.A, g)
              , O = function() {
                for (var e = [], t = 0; t < arguments.length; t++)
                    e[t] = arguments[t]
            };
            function N(e) {
                return p(this, arguments, void 0, function(e, t, n) {
                    var r, o, a;
                    return void 0 === t && (t = {}),
                    void 0 === n && (n = {}),
                    v(this, function(i) {
                        switch (i.label) {
                        case 0:
                            return [4, d.A.getMallIdAsync()];
                        case 1:
                            return r = i.sent(),
                            o = f(f(f({}, w), k), {
                                headers: f(f(f({}, I), null == n ? void 0 : n.headers), {
                                    mallid: r
                                })
                            }),
                            a = JSON.stringify(t || {}),
                            (0,
                            u.$A)(n) && (delete o.headers[j],
                            a = t),
                            [2, S((0,
                            u.oV)(m, e), f(f(f({}, o), {
                                body: a
                            }), n)).then(function(e) {
                                return (0,
                                u.Y4)(e, n)
                            })]
                        }
                    })
                })
            }
            function C(e) {
                return p(this, arguments, void 0, function(e, t, n) {
                    var r, o;
                    return void 0 === t && (t = {}),
                    void 0 === n && (n = {}),
                    v(this, function(a) {
                        return r = f(f(f({}, w), k), {
                            headers: f(f({}, I), null == n ? void 0 : n.headers)
                        }),
                        o = JSON.stringify(t || {}),
                        (0,
                        u.$A)(n) && (delete r.headers[j],
                        o = t),
                        [2, S((0,
                        u.oV)(m, e), f(f(f({}, r), {
                            body: o
                        }), n)).then(function(e) {
                            return (0,
                            u.Y4)(e, n)
                        })]
                    })
                })
            }
        }
        ,
        3376: (e, t, n) => {
            "use strict";
            n.d(t, {
                A: () => d,
                M: () => s
            });
            var r, o = n(89257), a = n(37171), i = n(19026), l = n(87781), c = n(3204), u = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return u([(0,
                    l.aI)()], e.prototype, "mallId", void 0),
                    u([(0,
                    l.kl)()], e.prototype, "mallName", void 0),
                    u([(0,
                    l.kl)()], e.prototype, "logo", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "mallStatus", void 0),
                    u([(0,
                    l.j4)()], e.prototype, "isSemiManagedMall", void 0),
                    e
                }();
                e.MalInfoListItem = t;
                var n = function() {
                    function e() {}
                    return u([(0,
                    l.kl)()], e.prototype, "companyName", void 0),
                    u([(0,
                    a.X)(), (0,
                    i.K)(), (0,
                    l.ZU)(function() {
                        return t
                    })], e.prototype, "malInfoList", void 0),
                    e
                }();
                e.CompanyListItem = n;
                var r = function() {
                    function e() {}
                    return u([(0,
                    l.aI)()], e.prototype, "userId", void 0),
                    u([(0,
                    l.kl)()], e.prototype, "maskMobile", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "accountType", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "accountStatus", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "tokenType", void 0),
                    u([(0,
                    a.X)(), (0,
                    i.K)(), (0,
                    l.ZU)(function() {
                        return n
                    })], e.prototype, "companyList", void 0),
                    e
                }();
                e.Res = r;
                var o = function() {
                    function e() {}
                    return u([(0,
                    a.X)(), (0,
                    i.K)(), (0,
                    l.ZU)(function() {
                        return r
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = o
            }(r || (r = {}));
            var s = (0,
            o.$)(function(e) {
                return (0,
                c.IM)("/bg/quiet/api/mms/userInfo", e)
            }, function() {
                return "useUserInfo"
            });
            const d = new function() {
                var e = this;
                this.mallId = null,
                this.setMallId = function(t) {
                    e.mallId = t
                }
                ,
                this.getMallId = function() {
                    var t;
                    return null !== (t = e.mallId) && void 0 !== t ? t : -1
                }
                ,
                this.getMallIdAsync = function() {
                    return t = e,
                    n = void 0,
                    o = function() {
                        var e, t, n, r, o, a, i, l, c, u, d, f;
                        return function(e, t) {
                            var n, r, o, a = {
                                label: 0,
                                sent: function() {
                                    if (1 & o[0])
                                        throw o[1];
                                    return o[1]
                                },
                                trys: [],
                                ops: []
                            }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                            return i.next = l(0),
                            i.throw = l(1),
                            i.return = l(2),
                            "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                return this
                            }
                            ),
                            i;
                            function l(l) {
                                return function(c) {
                                    return function(l) {
                                        if (n)
                                            throw new TypeError("Generator is already executing.");
                                        for (; i && (i = 0,
                                        l[0] && (a = 0)),
                                        a; )
                                            try {
                                                if (n = 1,
                                                r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                                0) : r.next) && !(o = o.call(r, l[1])).done)
                                                    return o;
                                                switch (r = 0,
                                                o && (l = [2 & l[0], o.value]),
                                                l[0]) {
                                                case 0:
                                                case 1:
                                                    o = l;
                                                    break;
                                                case 4:
                                                    return a.label++,
                                                    {
                                                        value: l[1],
                                                        done: !1
                                                    };
                                                case 5:
                                                    a.label++,
                                                    r = l[1],
                                                    l = [0];
                                                    continue;
                                                case 7:
                                                    l = a.ops.pop(),
                                                    a.trys.pop();
                                                    continue;
                                                default:
                                                    if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                                        a = 0;
                                                        continue
                                                    }
                                                    if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                                        a.label = l[1];
                                                        break
                                                    }
                                                    if (6 === l[0] && a.label < o[1]) {
                                                        a.label = o[1],
                                                        o = l;
                                                        break
                                                    }
                                                    if (o && a.label < o[2]) {
                                                        a.label = o[2],
                                                        a.ops.push(l);
                                                        break
                                                    }
                                                    o[2] && a.ops.pop(),
                                                    a.trys.pop();
                                                    continue
                                                }
                                                l = t.call(e, a)
                                            } catch (e) {
                                                l = [6, e],
                                                r = 0
                                            } finally {
                                                n = o = 0
                                            }
                                        if (5 & l[0])
                                            throw l[1];
                                        return {
                                            value: l[0] ? l[1] : void 0,
                                            done: !0
                                        }
                                    }([l, c])
                                }
                            }
                        }(this, function(p) {
                            switch (p.label) {
                            case 0:
                                if (this.mallId && -1 !== this.mallId)
                                    return [2, this.mallId];
                                p.label = 1;
                            case 1:
                                return p.trys.push([1, 3, , 4]),
                                [4, s({})];
                            case 2:
                                return e = p.sent(),
                                t = null === (i = null === (a = null === (o = null == e ? void 0 : e.companyList) || void 0 === o ? void 0 : o[0]) || void 0 === a ? void 0 : a.malInfoList) || void 0 === i ? void 0 : i.map(function(e) {
                                    return null == e ? void 0 : e.mallId
                                }),
                                n = localStorage.getItem("mall-info-id"),
                                r = null == t ? void 0 : t.find(function(e) {
                                    return n && e === Number(n)
                                }),
                                this.mallId = r || (null !== (f = null === (d = null === (u = null === (c = null === (l = null == e ? void 0 : e.companyList) || void 0 === l ? void 0 : l[0]) || void 0 === c ? void 0 : c.malInfoList) || void 0 === u ? void 0 : u[0]) || void 0 === d ? void 0 : d.mallId) && void 0 !== f ? f : -1),
                                [2, this.mallId];
                            case 3:
                                return p.sent(),
                                [3, 4];
                            case 4:
                                return [2]
                            }
                        })
                    }
                    ,
                    new ((r = void 0) || (r = Promise))(function(e, a) {
                        function i(e) {
                            try {
                                c(o.next(e))
                            } catch (e) {
                                a(e)
                            }
                        }
                        function l(e) {
                            try {
                                c(o.throw(e))
                            } catch (e) {
                                a(e)
                            }
                        }
                        function c(t) {
                            var n;
                            t.done ? e(t.value) : (n = t.value,
                            n instanceof r ? n : new r(function(e) {
                                e(n)
                            }
                            )).then(i, l)
                        }
                        c((o = o.apply(t, n || [])).next())
                    }
                    );
                    var t, n, r, o
                }
            }
        }
        ,
        10471: (e, t, n) => {
            "use strict";
            var r, o;
            n.d(t, {
                Q2: () => a,
                RJ: () => o,
                X2: () => r
            }),
            function(e) {
                e[e.NormalDot = 1] = "NormalDot",
                e[e.Modal = 2] = "Modal"
            }(r || (r = {})),
            function(e) {
                e[e.UnRead = 0] = "UnRead",
                e[e.Read = 1] = "Read"
            }(o || (o = {}));
            var a = 20
        }
        ,
        12027: (e, t, n) => {
            "use strict";
            n.d(t, {
                P: () => a
            });
            var r = n(36696)
              , o = n.n(r)
              , a = function(e, t) {
                void 0 === t && (t = "");
                var n = o().parse(window.location.search);
                return (null == n ? void 0 : n[e]) || t
            }
        }
        ,
        16297: (e, t, n) => {
            "use strict";
            var r = n(96540)
              , o = n(40961)
              , a = n(31022);
            o.render(r.createElement(a.Ay), document.getElementById("root"))
        }
        ,
        20195: (e, t, n) => {
            "use strict";
            n.d(t, {
                Ko: () => r.K,
                hR: () => r.h
            });
            var r = n(24206)
        }
        ,
        24206: (e, t, n) => {
            "use strict";
            n.d(t, {
                K: () => r,
                h: () => o
            });
            var r = "/settle"
              , o = {
                mainLogin: "".concat(r, "/login"),
                sellerLogin: "".concat(r, "/seller-login"),
                activityLogin: "".concat(r, "/activity-login"),
                mainSitePage: "".concat(r, "/site-main"),
                ruleCenter: "".concat(r, "/rule-center"),
                mainLoginRegister: "".concat(r, "/login/register"),
                mainLoginResetPassword: "".concat(r, "/login/reset-password"),
                mainLoginModifyMobile: "".concat(r, "/login/modify-mobile"),
                loginHosting: "".concat(r, "/login-hosting"),
                siteTrusteeship: "".concat(r, "/site-trusteeship"),
                login: "/login",
                loginRegister: "/login/register",
                loginMms: "/login/mms",
                mallMallEntry: "".concat(r, "/mall-entry"),
                mallAccountInfo: "/mall/account-info",
                loginResetPassword: "/login/reset-password",
                loginModifyMobile: "/login/modify-mobile",
                mallEnterpriseEntry: "".concat(r, "/enterprise-entry"),
                mallInfoEntry: "".concat(r, "/mall-entry"),
                entryLesson: "".concat(r, "/entry-lesson"),
                message: "".concat(r, "/message"),
                mainHome: "/main",
                graphService: "".concat(r, "/graph-service"),
                qualificationService: "".concat(r, "/qualification-service"),
                orderDetail: "".concat(r, "/order-detail"),
                orderList: "".concat(r, "/order-list"),
                entityRelate: "".concat(r, "/entity-relate"),
                mallTransfer: "".concat(r, "/mall-transfer"),
                relateAuthorization: "".concat(r, "/relate-authorization"),
                checkCompliance: "/main/check-compliance"
            }
        }
        ,
        24368: (e, t, n) => {
            "use strict";
            n.d(t, {
                t: () => Kt,
                A: () => Ht
            });
            var r, o = n(96540), a = n(53259), i = n.n(a), l = n(24180), c = n(73734), u = n(36696), s = n.n(u), d = n(33652), f = n(51026), p = n(3376), v = n(98959), m = n(61419), h = n(50953), y = n(24206), g = n(31022), b = n(33371), _ = n(38352), E = n(74022), j = n(20411), I = n(69603), w = n(36077), k = n(45269), S = "KEFU_CHAT", O = (0,
            o.createContext)(null), N = function() {
                return (0,
                o.useContext)(O)
            }, C = n(37171), R = n(19026), x = n(87781), P = n(45689), A = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return A([(0,
                    x.j4)()], e.prototype, "ableUseFlag", void 0),
                    A([(0,
                    x.j4)()], e.prototype, "subAccountAbleUse", void 0),
                    A([(0,
                    x.j4)()], e.prototype, "ableUseHumanChat", void 0),
                    A([(0,
                    x.j4)()], e.prototype, "ableUseSelfTool", void 0),
                    e
                }();
                e.Res = t;
                var n = function() {
                    function e() {}
                    return A([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return t
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = n
            }(r || (r = {}));
            var T, L = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return L([(0,
                    x.aI)()], e.prototype, "toolId", void 0),
                    L([(0,
                    x.kl)()], e.prototype, "toolName", void 0),
                    e
                }();
                e.ListItem = t;
                var n = function() {
                    function e() {}
                    return L([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return t
                    })], e.prototype, "list", void 0),
                    e
                }();
                e.Res = n;
                var r = function() {
                    function e() {}
                    return L([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return n
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = r
            }(T || (T = {}));
            var M, z = n(23549), U = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat1NodeVO = t;
                var n = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat2NodeVO = n;
                var r = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat3NodeVO = r;
                var o = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat4NodeVO = o;
                var a = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat5NodeVO = a;
                var i = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat6NodeVO = i;
                var l = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat7NodeVO = l;
                var c = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat8NodeVO = c;
                var u = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat9NodeVO = u;
                var s = function() {
                    function e() {}
                    return U([(0,
                    x.aI)()], e.prototype, "catId", void 0),
                    U([(0,
                    x.kl)()], e.prototype, "catName", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catType", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "catLevel", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "parentCatId", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isLeaf", void 0),
                    U([(0,
                    x.j4)()], e.prototype, "isHidden", void 0),
                    U([(0,
                    x.aI)()], e.prototype, "hiddenType", void 0),
                    e
                }();
                e.Cat10NodeVO = s;
                var d = function() {
                    function e() {}
                    return U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return t
                    })], e.prototype, "cat1NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return n
                    })], e.prototype, "cat2NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return r
                    })], e.prototype, "cat3NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return o
                    })], e.prototype, "cat4NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return a
                    })], e.prototype, "cat5NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return i
                    })], e.prototype, "cat6NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return l
                    })], e.prototype, "cat7NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return c
                    })], e.prototype, "cat8NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return u
                    })], e.prototype, "cat9NodeVO", void 0),
                    U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return s
                    })], e.prototype, "cat10NodeVO", void 0),
                    e
                }();
                e.CategoryPathVOListItem = d;
                var f = function() {
                    function e() {}
                    return U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return d
                    })], e.prototype, "categoryPathVOList", void 0),
                    e
                }();
                e.Res = f;
                var p = function() {
                    function e() {}
                    return U([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return f
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = p
            }(M || (M = {}));
            var B = n(9785)
              , D = function() {
                return D = Object.assign || function(e) {
                    for (var t, n = 1, r = arguments.length; n < r; n++)
                        for (var o in t = arguments[n])
                            Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
                    return e
                }
                ,
                D.apply(this, arguments)
            };
            function K(e) {
                var t = e.value
                  , n = e.onChange
                  , r = (0,
                o.useState)([])
                  , a = r[0]
                  , i = r[1];
                return o.createElement(B.default, {
                    placeholder: "请选择",
                    value: t,
                    onChange: n,
                    showSearch: !0,
                    onSearch: (0,
                    z.A)(function(e) {
                        return void 0 === e && (e = ""),
                        (t = {
                            searchText: e
                        },
                        (0,
                        P.b)(M.Response, "/marvel-supplier/api/ultraman/chat/reception/searchCategoryInfoByKeyWord", t, void 0)).then(function(e) {
                            var t = (e.categoryPathVOList || []).map(function(e) {
                                return t = e,
                                {
                                    label: (n = ["cat1NodeVO", "cat2NodeVO", "cat3NodeVO", "cat4NodeVO", "cat5NodeVO", "cat6NodeVO", "cat7NodeVO", "cat8NodeVO", "cat9NodeVO", "cat10NodeVO"].reduce(function(e, n) {
                                        return t[n] && e.push(t[n]),
                                        e
                                    }, [])).map(function(e) {
                                        return e.catName
                                    }).join(">"),
                                    value: n.map(function(e) {
                                        return e.catId
                                    }).join("-")
                                };
                                var t, n
                            });
                            i(t)
                        }).catch((0,
                        f.r)());
                        var t
                    }, 400),
                    options: a.map(function(e) {
                        return D(D({}, e), {
                            tooltip: {
                                width: 180
                            }
                        })
                    }),
                    width: 192,
                    dropdownProps: {
                        zIndex: 1e4
                    }
                })
            }
            var F = n(57953)
              , V = n(53509)
              , X = n(3739)
              , H = F.A.FormItem
              , Z = F.A.useForm;
            function q(e) {
                var t = e.onClose
                  , n = e.onResolve
                  , r = e.onCloseChat
                  , a = Z();
                return o.createElement("div", null, o.createElement("div", {
                    className: "category_desc__yDLgY"
                }, "亲，请选择您的经营类目，我们会按照类目分配专属客服为您提供更精准服务"), o.createElement(F.A, {
                    getForm: a,
                    onSubmit: function(e) {
                        var r = (e.category || "").split("-") || []
                          , o = r[r.length - 1];
                        null == n || n(o),
                        null == t || t()
                    }
                }, o.createElement(H, {
                    field: "category",
                    label: "经营类目",
                    required: !0,
                    labelWidth: 104
                }, o.createElement(K, null)), o.createElement("div", {
                    className: "category_btn__Idk8T"
                }, o.createElement(V.A, {
                    "data-tracking-id": "aNxOdvnioiU3r6UC",
                    marginLeft: "auto",
                    onClick: function() {
                        a.current.submit()
                    }
                }, "确认"), o.createElement(V.A, {
                    "data-tracking-id": "bhvl4HY8O-HhNXUZ",
                    onClick: function() {
                        null == t || t(),
                        null == r || r()
                    },
                    type: "gray"
                }, "取消"))))
            }
            var J, W = n(46220), Y = n(75657), G = n(65531), Q = n(3204), $ = n(31784), ee = n(19304), te = n(66597), ne = (J = function(e, t) {
                return J = Object.setPrototypeOf || {
                    __proto__: []
                }instanceof Array && function(e, t) {
                    e.__proto__ = t
                }
                || function(e, t) {
                    for (var n in t)
                        Object.prototype.hasOwnProperty.call(t, n) && (e[n] = t[n])
                }
                ,
                J(e, t)
            }
            ,
            function(e, t) {
                if ("function" != typeof t && null !== t)
                    throw new TypeError("Class extends value " + String(t) + " is not a constructor or null");
                function n() {
                    this.constructor = e
                }
                J(e, t),
                e.prototype = null === t ? Object.create(t) : (n.prototype = t.prototype,
                new n)
            }
            ), re = (0,
            Y.KV)(), oe = (0,
            $.Ln)(), ae = (0,
            $.DW)(), ie = new ee.Ay;
            ie.setScene(ee.ju.B_END_MMS).isProduct(re).setSignatureHost(oe).setUploadHost(ae).withCredentials().setTag("merchant-service-im").setMinSizeOfSplitUpload(5).setChunkSize(2).init();
            var le = function(e) {
                function t() {
                    return null !== e && e.apply(this, arguments) || this
                }
                return ne(t, e),
                t.prototype.beforeUpload = function(e) {
                    return t = this,
                    n = void 0,
                    o = function() {
                        return function(e, t) {
                            var n, r, o, a = {
                                label: 0,
                                sent: function() {
                                    if (1 & o[0])
                                        throw o[1];
                                    return o[1]
                                },
                                trys: [],
                                ops: []
                            }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                            return i.next = l(0),
                            i.throw = l(1),
                            i.return = l(2),
                            "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                return this
                            }
                            ),
                            i;
                            function l(l) {
                                return function(c) {
                                    return function(l) {
                                        if (n)
                                            throw new TypeError("Generator is already executing.");
                                        for (; i && (i = 0,
                                        l[0] && (a = 0)),
                                        a; )
                                            try {
                                                if (n = 1,
                                                r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                                0) : r.next) && !(o = o.call(r, l[1])).done)
                                                    return o;
                                                switch (r = 0,
                                                o && (l = [2 & l[0], o.value]),
                                                l[0]) {
                                                case 0:
                                                case 1:
                                                    o = l;
                                                    break;
                                                case 4:
                                                    return a.label++,
                                                    {
                                                        value: l[1],
                                                        done: !1
                                                    };
                                                case 5:
                                                    a.label++,
                                                    r = l[1],
                                                    l = [0];
                                                    continue;
                                                case 7:
                                                    l = a.ops.pop(),
                                                    a.trys.pop();
                                                    continue;
                                                default:
                                                    if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                                        a = 0;
                                                        continue
                                                    }
                                                    if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                                        a.label = l[1];
                                                        break
                                                    }
                                                    if (6 === l[0] && a.label < o[1]) {
                                                        a.label = o[1],
                                                        o = l;
                                                        break
                                                    }
                                                    if (o && a.label < o[2]) {
                                                        a.label = o[2],
                                                        a.ops.push(l);
                                                        break
                                                    }
                                                    o[2] && a.ops.pop(),
                                                    a.trys.pop();
                                                    continue
                                                }
                                                l = t.call(e, a)
                                            } catch (e) {
                                                l = [6, e],
                                                r = 0
                                            } finally {
                                                n = o = 0
                                            }
                                        if (5 & l[0])
                                            throw l[1];
                                        return {
                                            value: l[0] ? l[1] : void 0,
                                            done: !0
                                        }
                                    }([l, c])
                                }
                            }
                        }(this, function(t) {
                            return e ? /image/.test(e.type) ? e.size > 20971520 ? (W.default.warn("上传文件不大于20MB", void 0, void 0, {
                                getContainer: this.getContainer
                            }),
                            [2, !1]) : [2, !0] : (W.default.warn("请选择图片", void 0, void 0, {
                                getContainer: this.getContainer
                            }),
                            [2, !1]) : (W.default.warn("没有有效文件", void 0, void 0, {
                                getContainer: this.getContainer
                            }),
                            [2, !1])
                        })
                    }
                    ,
                    new ((r = Promise) || (r = Promise))(function(e, a) {
                        function i(e) {
                            try {
                                c(o.next(e))
                            } catch (e) {
                                a(e)
                            }
                        }
                        function l(e) {
                            try {
                                c(o.throw(e))
                            } catch (e) {
                                a(e)
                            }
                        }
                        function c(t) {
                            var n;
                            t.done ? e(t.value) : (n = t.value,
                            n instanceof r ? n : new r(function(e) {
                                e(n)
                            }
                            )).then(i, l)
                        }
                        c((o = o.apply(t, n || [])).next())
                    }
                    );
                    var t, n, r, o
                }
                ,
                t.prototype.uploadImage = function(e) {
                    var t = e.size >= ie.config.minSizeOfSplitUpload
                      , n = ie.uploadFile();
                    return t && n.setContentType(e.type),
                    n.setData(e).setHeaders({
                        "anti-content": (0,
                        G.xy)()
                    }),
                    n.build().then(function(e) {
                        return e.url
                    })
                }
                ,
                t.prototype.getContainer = function() {
                    return document.getElementById(S)
                }
                ,
                t
            }(te.n$);
            const ce = {
                enter: "content_enter__4BItt",
                textEnter: "content_textEnter__PA37j",
                send: "content_send__+qhy2",
                footBar: "content_footBar__g7kmc",
                appContent: "content_appContent__X2y-O",
                chatWindow: "content_chatWindow__Z4T3D",
                inlineBtnClose: "content_inlineBtnClose__wFFgd",
                inlineSend: "content_inlineSend__Nri4d",
                selfServiceToolList: "content_selfServiceToolList__WL-xF"
            };
            var ue = n(49795);
            (0,
            te.Nk)({
                get: Q.Jt,
                post: Q.bE
            });
            var se = function(e) {
                var t = e.IM
                  , n = (0,
                o.useState)([])
                  , r = n[0]
                  , a = n[1]
                  , i = (0,
                o.useState)(!1)
                  , l = i[0]
                  , c = i[1];
                return (0,
                o.useEffect)(function() {
                    var e;
                    l && (e = {},
                    (0,
                    P.b)(T.Response, "/marvel-supplier/api/ultraman/chat/reception/querySelfServiceTools", e, undefined)).then(function(e) {
                        var t = e.list;
                        a(null != t ? t : [])
                    }).catch((0,
                    f.r)())
                }, [l]),
                o.createElement(j.Ay, {
                    visible: l,
                    onVisibleChange: c,
                    content: (null == r ? void 0 : r.length) ? o.createElement("div", {
                        className: ce.selfServiceToolList
                    }, r.map(function(e, n) {
                        var r;
                        return o.createElement(V.A, {
                            "data-tracking-id": "c97xlzFRXzbZDt_l",
                            key: n,
                            type: "textPrimary",
                            size: "small",
                            margin: n ? "10px 0 0 0" : void 0,
                            onClick: function() {
                                var n;
                                null === (n = t.current) || void 0 === n || n.enter({
                                    content: e.toolName
                                }),
                                c(!1)
                            }
                        }, null !== (r = e.toolName) && void 0 !== r ? r : "-")
                    })) : null
                }, o.createElement(I.default, {
                    inlineSvg: o.createElement("svg", {
                        xmlns: "http://www.w3.org/2000/svg",
                        width: "16",
                        height: "16"
                    }, o.createElement("g", {
                        fill: "none",
                        fillRule: "evenodd",
                        stroke: "#0071F3"
                    }, o.createElement("path", {
                        strokeLinecap: "round",
                        d: "M6 6.5H2.667A.667.667 0 0 1 2 5.833V3.167c0-.369.298-.667.667-.667h10.666c.369 0 .667.298.667.667v2.666a.667.667 0 0 1-.667.667H12h0"
                    }), o.createElement("path", {
                        strokeLinejoin: "round",
                        d: "M9.05 4.5c.58 0 1.05.47 1.05 1.05V8.5H13a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H8.335a.67.67 0 0 1-.536-.268l-2.375-3.166a.815.815 0 0 1 1.142-1.142L8 11V5.55c0-.58.47-1.05 1.05-1.05Z"
                    }))),
                    fontSize: 16
                }), o.createElement(V.A, {
                    "data-tracking-id": "EJPfHThrM_wJxPST",
                    type: "textPrimary",
                    size: "small",
                    marginLeft: 4,
                    marginRight: 12
                }, "自助工具"))
            };
            function de() {
                var e = this
                  , t = (0,
                te.ub)()
                  , n = N()
                  , a = n.userId
                  , i = n.setShowChat
                  , l = (0,
                o.useState)(!1)
                  , c = l[0]
                  , u = l[1]
                  , s = (0,
                o.useState)()
                  , d = s[0]
                  , p = s[1];
                return (0,
                o.useEffect)(function() {
                    var e, n;
                    null === (e = t.current) || void 0 === e || e.eventEmitter.addListener("statusChange", function(e) {
                        var t = e.status
                          , n = e.checkInfo;
                        u("off" !== t && (null == n ? void 0 : n.resultCode) !== te.$9.机器人会话已存在或创建成功)
                    }),
                    (n = {},
                    (0,
                    P.b)(r.Response, "/marvel-supplier/api/ultraman/chat/reception/judgeAbleUseCustomerChat", n, undefined)).then(function(e) {
                        p(e.ableUseSelfTool)
                    }).catch((0,
                    f.r)())
                }, []),
                o.createElement(te.k_, {
                    External: le,
                    ref: t,
                    userId: a,
                    getCategory: function() {
                        return function(e) {
                            var t = e.closeChat;
                            return new Promise(function(e) {
                                var n = X.default.alert({
                                    title: "请选择您的经营类目",
                                    onClose: function() {
                                        n(),
                                        null == t || t()
                                    },
                                    content: o.createElement(q, {
                                        onResolve: e,
                                        onClose: function() {
                                            return n()
                                        },
                                        onCloseChat: t
                                    }),
                                    footer: null,
                                    showCloseIcon: !0,
                                    width: 408,
                                    getContainer: function() {
                                        return document.getElementById(S)
                                    },
                                    maskStyle: {
                                        borderRadius: 6
                                    },
                                    innerStyle: {
                                        top: "auto"
                                    }
                                }).close
                            }
                            )
                        }({
                            closeChat: function() {
                                null == i || i(!1)
                            }
                        })
                    }
                }, o.createElement(te.qw, null, o.createElement("div", {
                    className: ce.appContent
                }, o.createElement("div", {
                    className: ce.chatWindow
                }, o.createElement(te.Y_, null)), o.createElement("div", {
                    className: ce.footer
                }, o.createElement("div", {
                    className: ce.footBar
                }, c ? o.createElement(o.Fragment, null, o.createElement(ue.default, {
                    multiple: !1,
                    onPost: function(e) {
                        var n, r = e.file;
                        null === (n = t.current) || void 0 === n || n.sendPicture(r)
                    },
                    customTrigger: function(n) {
                        var r = n.open;
                        return o.createElement("div", {
                            className: ce.inlineSend,
                            onClick: function() {
                                return n = e,
                                o = void 0,
                                i = function() {
                                    var e;
                                    return function(e, t) {
                                        var n, r, o, a = {
                                            label: 0,
                                            sent: function() {
                                                if (1 & o[0])
                                                    throw o[1];
                                                return o[1]
                                            },
                                            trys: [],
                                            ops: []
                                        }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                                        return i.next = l(0),
                                        i.throw = l(1),
                                        i.return = l(2),
                                        "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                            return this
                                        }
                                        ),
                                        i;
                                        function l(l) {
                                            return function(c) {
                                                return function(l) {
                                                    if (n)
                                                        throw new TypeError("Generator is already executing.");
                                                    for (; i && (i = 0,
                                                    l[0] && (a = 0)),
                                                    a; )
                                                        try {
                                                            if (n = 1,
                                                            r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                                            0) : r.next) && !(o = o.call(r, l[1])).done)
                                                                return o;
                                                            switch (r = 0,
                                                            o && (l = [2 & l[0], o.value]),
                                                            l[0]) {
                                                            case 0:
                                                            case 1:
                                                                o = l;
                                                                break;
                                                            case 4:
                                                                return a.label++,
                                                                {
                                                                    value: l[1],
                                                                    done: !1
                                                                };
                                                            case 5:
                                                                a.label++,
                                                                r = l[1],
                                                                l = [0];
                                                                continue;
                                                            case 7:
                                                                l = a.ops.pop(),
                                                                a.trys.pop();
                                                                continue;
                                                            default:
                                                                if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                                                    a = 0;
                                                                    continue
                                                                }
                                                                if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                                                    a.label = l[1];
                                                                    break
                                                                }
                                                                if (6 === l[0] && a.label < o[1]) {
                                                                    a.label = o[1],
                                                                    o = l;
                                                                    break
                                                                }
                                                                if (o && a.label < o[2]) {
                                                                    a.label = o[2],
                                                                    a.ops.push(l);
                                                                    break
                                                                }
                                                                o[2] && a.ops.pop(),
                                                                a.trys.pop();
                                                                continue
                                                            }
                                                            l = t.call(e, a)
                                                        } catch (e) {
                                                            l = [6, e],
                                                            r = 0
                                                        } finally {
                                                            n = o = 0
                                                        }
                                                    if (5 & l[0])
                                                        throw l[1];
                                                    return {
                                                        value: l[0] ? l[1] : void 0,
                                                        done: !0
                                                    }
                                                }([l, c])
                                            }
                                        }
                                    }(this, function(n) {
                                        switch (n.label) {
                                        case 0:
                                            return [4, null === (e = t.current) || void 0 === e ? void 0 : e.beforeSyncPush()];
                                        case 1:
                                            return "end" === n.sent() || r(),
                                            [2]
                                        }
                                    })
                                }
                                ,
                                new ((a = void 0) || (a = Promise))(function(e, t) {
                                    function r(e) {
                                        try {
                                            c(i.next(e))
                                        } catch (e) {
                                            t(e)
                                        }
                                    }
                                    function l(e) {
                                        try {
                                            c(i.throw(e))
                                        } catch (e) {
                                            t(e)
                                        }
                                    }
                                    function c(t) {
                                        var n;
                                        t.done ? e(t.value) : (n = t.value,
                                        n instanceof a ? n : new a(function(e) {
                                            e(n)
                                        }
                                        )).then(r, l)
                                    }
                                    c((i = i.apply(n, o || [])).next())
                                }
                                );
                                var n, o, a, i
                            }
                        }, o.createElement(I.default, {
                            inlineSvg: o.createElement("svg", {
                                viewBox: "0 0 1024 1024"
                            }, o.createElement("path", {
                                d: "M870.4 128c49.5 0 89.6 39.4 89.6 88l0 592c0 48.6-40.1 88-89.6 88l-716.8 0c-49.5 0-89.6-39.4-89.6-88l0-592c0-48.6 40.1-88 89.6-88l716.8 0z m0 70.4l-716.8 0c-9.9 0-17.9 7.9-17.9 17.6l0 592c0 9.7 8 17.6 17.9 17.6l716.8 0c9.9 0 17.9-7.9 17.9-17.6l0-592c0-9.7-8-17.6-17.9-17.6z m-182.8 237l163.6 163.4c13.9 13.9 13.7 36.2-0.5 49.8-14.1 13.6-36.8 13.4-50.7-0.5l-136-135.9-217.5 251.4c-11.2 13-29.9 16-44.5 8.1l-6-4.1c-15.1-12.6-16.9-34.8-4.1-49.6l242.9-280.7c13.6-15.8 38.1-16.6 52.8-1.9z m-315.9 77.7l72.7 70.5c14.1 13.7 14.2 35.9 0.3 49.7-13.9 13.8-36.6 14-50.7 0.4l-45.8-44.4-93.6 104.1c-11.5 12.7-30.3 15.4-44.7 7.2l-5.9-4.2c-14.8-12.9-16.2-35.1-3.1-49.7l118.8-131.8c13.6-15.1 37.4-15.9 52-1.8z m-21-212.3c39.6 0 71.7 31.5 71.7 70.4 0 38.9-32.1 70.4-71.7 70.4-39.6 0-71.7-31.5-71.7-70.4 0-38.9 32.1-70.4 71.7-70.4z"
                            })),
                            fontSize: 17
                        }), o.createElement(V.A, {
                            "data-tracking-id": "fTBk86pju1hkl1Ze",
                            type: "textPrimary",
                            size: "small",
                            marginLeft: 4
                        }, "发图片"))
                    }
                }), o.createElement("div", {
                    onClick: function() {
                        var e;
                        null === (e = t.current) || void 0 === e || e.close()
                    },
                    className: ce.inlineBtnClose
                }, o.createElement(I.default, {
                    inlineSvg: o.createElement("svg", {
                        viewBox: "0 0 1024 1024"
                    }, o.createElement("path", {
                        d: "M699.1 104.9c108 49.6 192.5 139.9 233.9 253.9 84.6 232.5-35.3 489.6-267.8 574.2-232.5 84.6-489.6-35.3-574.2-267.8-79.8-219.2 22.2-462.5 232.7-559.7 19.3-8.9 42.1-0.5 51 18.7 8.9 19.3 0.5 42.1-18.8 51-174.3 80.5-258.9 282.1-192.7 463.8 70.1 192.6 283.1 292 475.8 221.8 192.6-70.1 292-283.1 221.8-475.8-34.4-94.5-104.3-169.2-193.8-210.3-19.3-8.9-27.7-31.7-18.9-50.9 8.9-19.3 31.7-27.7 51-18.9z m-187.1-40.9c21.2 0 38.4 17.2 38.4 38.4l0 435.2c0 21.2-17.2 38.4-38.4 38.4-21.2 0-38.4-17.2-38.4-38.4l0-435.2c0-21.2 17.2-38.4 38.4-38.4z"
                    })),
                    fontSize: 16
                }), o.createElement(V.A, {
                    "data-tracking-id": "RZ5iSBmC4FufYI4W",
                    type: "textPrimary",
                    size: "small",
                    marginLeft: 4
                }, "结束人工客服"))) : d ? o.createElement(se, {
                    IM: t
                }) : null), o.createElement("div", {
                    className: ce.enter
                }, o.createElement("div", {
                    className: ce.textEnter
                }, o.createElement(te.fs, {
                    placeholder: "请用文字简单描述你遇到的问题"
                })), o.createElement("div", {
                    className: ce.send
                }, o.createElement(V.A, {
                    "data-tracking-id": "1wtdOAjNkNKolqkv",
                    onClick: function() {
                        var e;
                        null === (e = t.current) || void 0 === e || e.enter()
                    }
                }, "发送")))))))
            }
            function fe(e) {
                var t = e.onClose
                  , n = N().setShowChat;
                return o.createElement("div", {
                    className: "header_header__slf9H"
                }, o.createElement("span", {
                    className: "header_headerTitleText__7RQqf",
                    onClick: function() {
                        return null == n ? void 0 : n(!1)
                    }
                }, o.createElement(I.default, {
                    type: "left",
                    fontSize: 16,
                    marginRight: 4
                }), o.createElement("span", null, "返回")), o.createElement("div", {
                    className: "header_headCenter__cnOt1"
                }, o.createElement("span", null, "TEMU官方客服"), o.createElement("div", {
                    className: "header_headTag__CrSj-"
                }, "官方")), o.createElement(I.default, {
                    type: "close-circle_filled",
                    fontSize: 16,
                    marginLeft: 12,
                    color: "#fff",
                    style: {
                        cursor: "pointer"
                    },
                    onClick: t
                }))
            }
            function pe(e) {
                var t = e.onClose;
                return o.createElement("div", {
                    className: "chat_chat__tOh7e",
                    id: S
                }, o.createElement(fe, {
                    onClose: t
                }), o.createElement("div", {
                    className: "chat_contentWrapper__EJkLJ"
                }, o.createElement(te.tH, null, o.createElement(de, null))))
            }
            var ve = (0,
            o.createContext)(null)
              , me = function() {
                return (0,
                o.useContext)(ve)
            };
            const he = "footer_footer__LppCs";
            function ye() {
                var e = N().setShowChat;
                return o.createElement("div", {
                    className: he
                }, o.createElement(V.A, {
                    prefix: "message",
                    type: "textPrimary",
                    onClick: function() {
                        return null == e ? void 0 : e(!0)
                    },
                    "data-tracking-id": "snR9e_eyJXqNX-xb"
                }, "联系客服"))
            }
            function ge() {
                var e = N().setShowChat;
                return o.createElement("div", {
                    className: he
                }, o.createElement("div", {
                    className: "footer_footerMask__GbnPg"
                }), o.createElement(V.A, {
                    "data-tracking-id": "SDZuWwJcCW1HQm0h",
                    onClick: function() {
                        return null == e ? void 0 : e(!0)
                    }
                }, "联系官方客服"))
            }
            var be, _e = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return _e([(0,
                    x.aI)()], e.prototype, "id", void 0),
                    _e([(0,
                    x.kl)()], e.prototype, "title", void 0),
                    _e([(0,
                    x.kl)()], e.prototype, "answer", void 0),
                    _e([(0,
                    x.aI)()], e.prototype, "answerType", void 0),
                    e
                }();
                e.Res = t;
                var n = function() {
                    function e() {}
                    return _e([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return t
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = n
            }(be || (be = {}));
            function Ee() {
                var e = me().detail
                  , t = (0,
                o.useState)()
                  , n = t[0]
                  , r = t[1];
                return (0,
                o.useEffect)(function() {
                    var t, n = null == e ? void 0 : e.detailId;
                    null != n && (t = {
                        id: n
                    },
                    (0,
                    P.b)(be.Response, "/marvel-supplier/api/ultraman/chat/knowledge/queryKnowledgeBaseDetail", t, undefined)).then(function(e) {
                        r(e)
                    }).catch((0,
                    f.r)())
                }, [null == e ? void 0 : e.detailId]),
                o.createElement("div", null, o.createElement("div", {
                    className: "content_title__MoJP2"
                }, null == n ? void 0 : n.title), o.createElement("div", {
                    dangerouslySetInnerHTML: {
                        __html: (null == n ? void 0 : n.answer) || ""
                    },
                    className: "content_desc__QJVUM"
                }))
            }
            function je(e) {
                var t = e.onClose
                  , n = me().setDetail;
                return o.createElement("div", {
                    className: "header_header__v0MJI"
                }, o.createElement("span", {
                    className: "header_headerTitleText__4F8Ri",
                    onClick: function() {
                        return null == n ? void 0 : n({
                            detailId: null
                        })
                    }
                }, o.createElement(I.default, {
                    type: "left",
                    fontSize: 16,
                    marginRight: 4
                }), o.createElement("span", null, "返回")), o.createElement(I.default, {
                    type: "close-circle_filled",
                    fontSize: 16,
                    color: "#fff",
                    style: {
                        cursor: "pointer"
                    },
                    onClick: t
                }))
            }
            function Ie(e) {
                var t = e.onClose;
                return o.createElement("div", {
                    className: "detail_detail__YJxKH"
                }, o.createElement(je, {
                    onClose: t
                }), o.createElement("div", {
                    className: "detail_detailContentInner__Qi-3O"
                }, o.createElement(Ee, null)), o.createElement(ye, null))
            }
            var we, ke = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return ke([(0,
                    x.aI)()], e.prototype, "id", void 0),
                    ke([(0,
                    x.kl)()], e.prototype, "title", void 0),
                    e
                }();
                e.KnowledgeBaseTitleListItem = t;
                var n = function() {
                    function e() {}
                    return ke([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return t
                    })], e.prototype, "knowledgeBaseTitleList", void 0),
                    ke([(0,
                    x.aI)()], e.prototype, "total", void 0),
                    e
                }();
                e.Res = n;
                var r = function() {
                    function e() {}
                    return ke([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return n
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = r
            }(we || (we = {}));
            var Se = n(80193);
            function Oe() {
                var e = (0,
                o.useState)([])
                  , t = e[0]
                  , n = e[1]
                  , r = me().setDetail
                  , a = (0,
                o.useState)({
                    page: 1,
                    pageSize: 20
                })[0];
                return (0,
                o.useEffect)(function() {
                    var e;
                    (e = a,
                    (0,
                    P.b)(we.Response, "/marvel-supplier/api/ultraman/chat/knowledge/pageQueryKnowledgeBaseTitleList", e, undefined)).then(function(e) {
                        n(t.concat((null == e ? void 0 : e.knowledgeBaseTitleList) || []))
                    }).catch((0,
                    f.r)())
                }, [a]),
                o.createElement("div", null, o.createElement("div", {
                    className: "default_defaultTitle__GlQqU"
                }, "您可能想知道"), o.createElement("div", null, t.map(function(e) {
                    return o.createElement("div", {
                        className: "default_defaultItem__0-9re",
                        key: e.id,
                        onClick: function() {
                            return null == r ? void 0 : r({
                                detailId: e.id
                            })
                        }
                    }, o.createElement("div", {
                        className: "default_defaultItemDot__PXpUy"
                    }), o.createElement("div", {
                        className: "default_defaultItemText__FQcDe"
                    }, o.createElement(Se.default, {
                        lines: 1
                    }, o.createElement("span", null, e.title))))
                })))
            }
            var Ne, Ce = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return Ce([(0,
                    x.aI)()], e.prototype, "id", void 0),
                    Ce([(0,
                    x.kl)()], e.prototype, "title", void 0),
                    Ce([(0,
                    x.kl)()], e.prototype, "answer", void 0),
                    Ce([(0,
                    x.aI)()], e.prototype, "answerType", void 0),
                    e
                }();
                e.KnowledgeBaseSimpleInfoListItem = t;
                var n = function() {
                    function e() {}
                    return Ce([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return t
                    })], e.prototype, "knowledgeBaseSimpleInfoList", void 0),
                    Ce([(0,
                    x.aI)()], e.prototype, "total", void 0),
                    e
                }();
                e.Res = n;
                var r = function() {
                    function e() {}
                    return Ce([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return n
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = r
            }(Ne || (Ne = {}));
            var Re = function(e, t) {
                return (0,
                P.b)(Ne.Response, "/marvel-supplier/api/ultraman/chat/knowledge/pageQueryKnowledgeBaseList", e, t)
            };
            const xe = n.p + "4a2825d5702c37b4a9c0.png";
            var Pe = n(39677);
            function Ae() {
                return o.createElement(Pe.default, {
                    img: xe,
                    title: o.createElement("span", {
                        style: {
                            color: "rgba(0,0,0,0.80)",
                            fontWeight: 400,
                            fontSize: 14
                        }
                    }, "暂无相关结果"),
                    description: "您可尝试更换关键词",
                    height: "100%"
                })
            }
            var Te = /[`~!@#_$%^&*()=|{}':;',\\\[\\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？\s]/g;
            function Le(e) {
                var t = e.item
                  , n = e.searchValue
                  , r = e.onLinkToDetail
                  , a = function(e, t) {
                    if (!t)
                        return e;
                    var n = new RegExp("".concat(t).replace(Te, function(e) {
                        return "\\".concat(e)
                    }),"g");
                    return e.replace(n, "<span style='color: #FB7701'>".concat(t, "</span>"))
                };
                return o.createElement("div", {
                    className: "item_searchItem__JnRtp",
                    onClick: r
                }, o.createElement("div", {
                    className: "item_searchItemInner__R34ab"
                }, o.createElement("div", {
                    className: "item_searchItemTitle__KJd3f",
                    dangerouslySetInnerHTML: {
                        __html: a((null == t ? void 0 : t.title) || "", n)
                    }
                }), o.createElement("div", {
                    className: "item_searchItemDesc__klWFp"
                }, o.createElement(Se.default, {
                    lines: 2
                }, o.createElement("span", {
                    dangerouslySetInnerHTML: {
                        __html: a(function(e) {
                            if (e) {
                                var t = e.replace(/<[^>]+>/g, "");
                                if (-1 !== t.indexOf("<")) {
                                    var n = t.indexOf("<") + 1;
                                    /^[A-Za-z]+$/.test(t[n]) && (t = t.split("<")[0])
                                }
                                return t.replace(/&nbsp;/g, " ")
                            }
                            return ""
                        }((null == t ? void 0 : t.answer) || ""), n)
                    }
                })))))
            }
            var Me = n(41534)
              , ze = n(38672)
              , Ue = function() {
                return Ue = Object.assign || function(e) {
                    for (var t, n = 1, r = arguments.length; n < r; n++)
                        for (var o in t = arguments[n])
                            Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
                    return e
                }
                ,
                Ue.apply(this, arguments)
            };
            function Be(e) {
                var t = e.searchValue
                  , n = (0,
                o.useState)([])
                  , r = n[0]
                  , a = n[1]
                  , i = (0,
                o.useState)({
                    page: 1,
                    pageSize: 20
                })
                  , l = i[0]
                  , c = i[1]
                  , u = o.useState("")
                  , s = u[0]
                  , d = u[1]
                  , p = me().setDetail
                  , v = (0,
                o.useState)(!1)
                  , m = v[0]
                  , h = v[1];
                return (0,
                o.useEffect)(function() {
                    h(!0),
                    c({
                        page: 1,
                        pageSize: 20
                    }),
                    Re(Ue(Ue({}, l), {
                        content: t
                    })).then(function(e) {
                        var t = (null == e ? void 0 : e.knowledgeBaseSimpleInfoList) || []
                          , n = e.total || 0;
                        a(t),
                        d(n === t.length ? "nomore" : "")
                    }).catch((0,
                    f.r)()).finally(function() {
                        h(!1)
                    })
                }, [t]),
                m ? o.createElement("div", {
                    className: "search-result_loading__b+vdA"
                }, o.createElement(Me.default, {
                    spinning: m
                })) : 0 === r.length ? o.createElement(Ae, null) : o.createElement(ze.default, {
                    onLoad: function(e, n) {
                        if ("nomore" !== s) {
                            d("loading");
                            var o = {
                                page: l.page + 1,
                                pageSize: l.pageSize
                            };
                            c(o),
                            Re(Ue(Ue({}, o), {
                                content: t
                            })).then(function(e) {
                                var t = r.concat((null == e ? void 0 : e.knowledgeBaseSimpleInfoList) || [])
                                  , o = e.total || 0;
                                a(t),
                                d(o === t.length ? "nomore" : ""),
                                n()
                            }).catch(function(e) {
                                W.default.warn((null == e ? void 0 : e.errorMsg) || "")
                            })
                        }
                    },
                    status: s
                }, r.map(function(e) {
                    return o.createElement(Le, {
                        key: e.id,
                        item: e,
                        searchValue: t,
                        onLinkToDetail: function() {
                            null == p || p({
                                detailId: e.id
                            })
                        }
                    })
                }))
            }
            function De(e) {
                var t = e.searchValue;
                return o.createElement("div", {
                    className: "content_content__glhCs"
                }, "" === t ? o.createElement(Oe, null) : o.createElement(Be, {
                    searchValue: t
                }))
            }
            var Ke = n(9560);
            var Fe = n(62519);
            function Ve(e) {
                var t = e.onSearch
                  , n = e.onClose
                  , r = (0,
                o.useState)("")
                  , a = r[0]
                  , i = r[1]
                  , l = (0,
                Ke.A)(a, 300);
                return (0,
                o.useEffect)(function() {
                    null == t || t(l)
                }, [l]),
                o.createElement("div", null, o.createElement("div", {
                    className: "header_header__rnFgD"
                }, o.createElement("div", {
                    className: "header_headerTitle__CXRp3"
                }, o.createElement("span", {
                    className: "header_headerTitleText__OXEv6"
                }, "官方客服"), o.createElement(I.default, {
                    type: "close-circle_filled",
                    fontSize: 16,
                    color: "#fff",
                    style: {
                        cursor: "pointer"
                    },
                    onClick: n
                })), o.createElement("div", null, o.createElement(Fe.A, {
                    hideBoxShadow: !0,
                    value: a,
                    onChange: i,
                    placeholder: "搜一搜想问的问题",
                    allowClear: !0,
                    prefix: o.createElement(I.default, {
                        type: "search",
                        fontSize: 12
                    })
                }))))
            }
            function Xe(e) {
                var t = e.onClose
                  , n = (0,
                o.useState)("")
                  , r = n[0]
                  , a = n[1];
                return o.createElement("div", {
                    className: "search_searchContent__TWlqF"
                }, o.createElement(Ve, {
                    onSearch: a,
                    onClose: t
                }), o.createElement("div", {
                    className: "search_wrapperContentInner__90djU"
                }, o.createElement(De, {
                    searchValue: r
                })), o.createElement(ge, null))
            }
            function He(e) {
                var t = e.onClose
                  , n = (0,
                o.useState)({
                    detailId: null
                })
                  , r = n[0]
                  , a = n[1];
                return o.createElement(ve.Provider, {
                    value: {
                        detail: r,
                        setDetail: a
                    }
                }, o.createElement("div", {
                    className: "search_search__-I-fw"
                }, null !== r.detailId ? o.createElement(Ie, {
                    onClose: t
                }) : o.createElement(Xe, {
                    onClose: t
                })))
            }
            function Ze(e) {
                var t = e.onClose
                  , n = e.userId
                  , r = e.isCustomerChat
                  , a = (0,
                o.useState)(!1)
                  , i = a[0]
                  , l = a[1]
                  , c = (0,
                o.useRef)(null);
                return (0,
                o.useEffect)(function() {
                    r && l(!0)
                }, [r]),
                (0,
                o.useEffect)(function() {
                    var e = (0,
                    E.A)(function() {
                        if (c.current) {
                            var e = c.current.getBoundingClientRect().top
                              , t = (document.documentElement.clientHeight || document.body.clientHeight) - e - 20
                              , n = t;
                            t >= 622 ? n = 622 : t < 360 && (n = 360),
                            c.current.style.height = "".concat(n, "px")
                        }
                    }, 100);
                    return e(),
                    window.addEventListener("resize", e),
                    function() {
                        window.removeEventListener("resize", e)
                    }
                }, []),
                o.createElement(O.Provider, {
                    value: {
                        showChat: i,
                        setShowChat: l,
                        userId: n
                    }
                }, o.createElement("div", {
                    className: "kefu_chatAndSearch__W5MrA",
                    ref: c
                }, i ? o.createElement(pe, {
                    onClose: t
                }) : o.createElement(He, {
                    onClose: t
                })))
            }
            function qe(e) {
                var t, n, r, a = e.userId, i = (0,
                o.useState)(!1), l = i[0], c = i[1], u = (0,
                o.useState)(!1), s = u[0], d = u[1], f = null === (t = [y.h.mallInfoEntry, y.h.mallMallEntry]) || void 0 === t ? void 0 : t.find(function(e) {
                    var t;
                    return -1 !== (null === (t = null === location || void 0 === location ? void 0 : location.pathname) || void 0 === t ? void 0 : t.indexOf(e))
                }), p = function(e) {
                    (0,
                    k.i_)({
                        pageSn: 20846,
                        op: "pv",
                        user_source: e
                    })
                };
                (0,
                w.cn)() && [y.h.mainSitePage].includes(location.pathname) && (null === (r = null === (n = (0,
                w.pB)()) || void 0 === n ? void 0 : n.useSellerCenterEvent) || void 0 === r || r.call(n, function(e) {
                    if ("start_customer_service" === e) {
                        p(1),
                        c(!0),
                        d(!0);
                        var t = new URL(location.href);
                        t.searchParams.set("isCustomerChat", "true"),
                        window.history.pushState({}, document.title, t.href)
                    }
                }));
                var v = function() {
                    var e = new URL(location.href);
                    e.searchParams.delete("isCustomerChat"),
                    window.history.pushState({}, document.title, e.href)
                };
                return o.createElement("div", {
                    className: "kefu_kefuOuter__t+3Nh"
                }, o.createElement(j.Ay, {
                    content: o.createElement(Ze, {
                        isCustomerChat: s,
                        onClose: function() {
                            v(),
                            c(!1)
                        },
                        userId: a
                    }),
                    portalPadding: 0,
                    placement: "bottom",
                    topOffset: 28,
                    leftOffset: -70,
                    animation: {
                        enter: !1,
                        exit: !1
                    },
                    visible: l,
                    autoPlace: !1,
                    isNearBy: !0,
                    triggerWrapperProps: {
                        style: {
                            whiteSpace: "initial"
                        }
                    },
                    withArrow: !1
                }, f ? o.createElement("div", {
                    className: "kefu_temuKefu__xEL0L",
                    onClick: function() {
                        v(),
                        l ? c(!1) : (p(2),
                        c(!0))
                    }
                }, o.createElement(I.default, {
                    inlineSvg: o.createElement("svg", {
                        viewBox: "0 0 1024 1024"
                    }, o.createElement("path", {
                        d: "M512 60.8c190.8 0 349.2 139.6 381.3 323.1l33.4 0.1c54.1 0 97.9 43.8 97.9 97.9l0 0 0 92.2c0 54.1-43.8 97.9-97.9 97.9l0 0-30 0c-20 164.1-158.3 291.2-326 291.2-19.4 0-35.2-15.8-35.2-35.2 0-19.4 15.8-35.2 35.2-35.2 138.1 0 251.1-110.2 257.8-248.9l0.3-13 0-178.3c0-177.6-141.9-321.4-316.8-321.4-170.3 0-309.3 136.3-316.5 307.4l-0.1 1.9c0.3 2.4 0.4 4.9 0.4 7.5l0 160c0 35.3-28.7 64-64 64l-33.9 0c-54.1 0-97.9-43.8-97.9-97.9l0-92.2c0-54.1 43.8-97.9 97.9-97.9l32.8-0.1c32.1-183.6 190.5-323.1 381.3-323.1z m128 448c19.4 0 35.2 15.8 35.2 35.2 0 90.1-73.1 163.2-163.2 163.2-90.1 0-163.2-73.1-163.2-163.2 0-19.4 15.8-35.2 35.2-35.2 19.4 0 35.2 15.8 35.2 35.2 0 51.3 41.5 92.8 92.8 92.8 51.3 0 92.8-41.5 92.8-92.8 0-19.4 15.8-35.2 35.2-35.2z m-515.3-54.5l-26.8 0.1c-13.5 0-24.7 9.7-27.1 22.6l-0.4 4.9 0 92.2c0 13.5 9.7 24.7 22.6 27.1l4.9 0.4 27.5 0 0-19.1c0-0.3-0.1-0.5-0.1-0.8l-0.5-5.7-0.1-121.7z m802 0.1l-27.4 0 0 147.2 27.4 0 5-0.4c12.8-2.3 22.6-13.6 22.5-27.1l0 0 0-92.2-0.4-4.9c-2.3-12.8-13.6-22.6-27.1-22.6l0 0z"
                    })),
                    marginRight: 4,
                    fontSize: 20
                }), "客服") : o.createElement("div", {
                    className: "kefu_kefu__KQF6P",
                    onClick: function() {
                        v(),
                        l ? c(!1) : (p(2),
                        c(!0))
                    }
                }, o.createElement("div", {
                    className: "kefu_icon__8Tr2N"
                }), "官方客服")))
            }
            function Je() {
                var e = (0,
                o.useState)(null)
                  , t = e[0]
                  , n = e[1];
                return (0,
                o.useEffect)(function() {
                    (0,
                    p.M)({}).then(function(e) {
                        n(e)
                    }).catch((0,
                    f.r)())
                }, []),
                t && o.createElement(qe, {
                    userId: t.userId
                })
            }
            var We, Ye = n(50016), Ge = n(12027), Qe = n(10471), $e = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return $e([(0,
                    x.aI)()], e.prototype, "unreadMsgNum", void 0),
                    e
                }();
                e.Res = t;
                var n = function() {
                    function e() {}
                    return $e([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return t
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = n
            }(We || (We = {}));
            var et, tt = function(e, t) {
                return (0,
                P.b)(We.Response, "/bg/quick/api/kj/merchant/msgBox/totalUnreadMsgNum", e, t)
            }, nt = n(36668), rt = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return rt([(0,
                    x.aI)()], e.prototype, "id", void 0),
                    rt([(0,
                    x.kl)()], e.prototype, "title", void 0),
                    rt([(0,
                    x.kl)()], e.prototype, "content", void 0),
                    rt([(0,
                    x.aI)()], e.prototype, "sendTime", void 0),
                    rt([(0,
                    x.aI)()], e.prototype, "readStatus", void 0),
                    rt([(0,
                    x.kl)()], e.prototype, "jumpUrl", void 0),
                    rt([(0,
                    x.kl)()], e.prototype, "linkText", void 0),
                    rt([(0,
                    x.aI)()], e.prototype, "msgActionType", void 0),
                    rt([(0,
                    x.j4)()], e.prototype, "forceRead", void 0),
                    rt([(0,
                    x.aI)()], e.prototype, "forceReadSecond", void 0),
                    rt([(0,
                    x.j4)()], e.prototype, "isRichContent", void 0),
                    rt([(0,
                    C.X)(), (0,
                    nt.IP)({
                        each: !0
                    })], e.prototype, "fileUrl", void 0),
                    e
                }();
                e.UnreadPopMsgItem = t;
                var n = function() {
                    function e() {}
                    return rt([(0,
                    x.aI)()], e.prototype, "num", void 0),
                    rt([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return t
                    })], e.prototype, "unreadPopMsg", void 0),
                    e
                }();
                e.Res = n;
                var r = function() {
                    function e() {}
                    return rt([(0,
                    C.X)(), (0,
                    R.K)(), (0,
                    x.ZU)(function() {
                        return n
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = r
            }(et || (et = {}));
            var ot = n(46942)
              , at = n.n(ot)
              , it = n(78378)
              , lt = n(74353)
              , ct = n.n(lt)
              , ut = n(93865)
              , st = n(48387).Ay.Meta;
            const dt = function(e) {
                var t = e.data
                  , n = e.onClick
                  , r = e.onReadClick
                  , a = e.onJumpUrlClick
                  , i = t.title
                  , l = t.content
                  , c = t.sendTime
                  , u = t.jumpUrl
                  , s = t.linkText
                  , d = t.fileUrl;
                return o.createElement("div", {
                    className: at()(ut.A.card, ut.A.slimCard)
                }, o.createElement("div", {
                    className: ut.A.readStatusDot,
                    onClick: r
                }, o.createElement("span", {
                    className: at()(ut.A.default, ut.A.unreadDot)
                })), o.createElement("div", {
                    className: ut.A.content,
                    onClick: n
                }, o.createElement(st, {
                    header: o.createElement("div", {
                        className: ut.A.slimHeader
                    }, o.createElement("span", {
                        className: ut.A.title
                    }, i || ""), !!(null == d ? void 0 : d.length) && o.createElement("div", null, o.createElement(I.default, {
                        type: "clip",
                        fontSize: 14
                    }))),
                    body: o.createElement(o.Fragment, null, (0,
                    b.Fh)(l) ? o.createElement("div", {
                        className: at()(ut.A.slimBody, ut.A.unreadBody)
                    }) : o.createElement("div", {
                        className: at()(ut.A.slimBody, ut.A.unreadBody),
                        dangerouslySetInnerHTML: {
                            __html: l || ""
                        }
                    })),
                    footer: o.createElement("div", {
                        className: at()(ut.A.footer, ut.A.slimFooter, ut.A.unreadBody)
                    }, c ? ct()(c).format("YYYY-MM-DD HH:mm:ss") : void 0, s && u ? o.createElement(V.A, {
                        type: "textPrimary",
                        size: "small",
                        onClick: function(e) {
                            e.stopPropagation(),
                            e.preventDefault(),
                            null == a || a(),
                            window.open(u || "")
                        },
                        "data-tracking-id": "JW9esULH4UWY-3dq"
                    }, s) : o.createElement("span", null)),
                    headerLines: 1,
                    bodyLines: 1,
                    ellipsisProps: {
                        header: {
                            tooltip: !1,
                            children: null
                        },
                        body: {
                            tooltip: !1,
                            children: null
                        }
                    }
                })))
            };
            var ft = n(47145)
              , pt = n(89664)
              , vt = n(54670)
              , mt = n(95367);
            var ht = n(14669)
              , yt = n(57936)
              , gt = n(30191)
              , bt = function(e, t, n, r) {
                return new (n || (n = Promise))(function(o, a) {
                    function i(e) {
                        try {
                            c(r.next(e))
                        } catch (e) {
                            a(e)
                        }
                    }
                    function l(e) {
                        try {
                            c(r.throw(e))
                        } catch (e) {
                            a(e)
                        }
                    }
                    function c(e) {
                        var t;
                        e.done ? o(e.value) : (t = e.value,
                        t instanceof n ? t : new n(function(e) {
                            e(t)
                        }
                        )).then(i, l)
                    }
                    c((r = r.apply(e, t || [])).next())
                }
                )
            }
              , _t = function(e, t) {
                var n, r, o, a = {
                    label: 0,
                    sent: function() {
                        if (1 & o[0])
                            throw o[1];
                        return o[1]
                    },
                    trys: [],
                    ops: []
                }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                return i.next = l(0),
                i.throw = l(1),
                i.return = l(2),
                "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                    return this
                }
                ),
                i;
                function l(l) {
                    return function(c) {
                        return function(l) {
                            if (n)
                                throw new TypeError("Generator is already executing.");
                            for (; i && (i = 0,
                            l[0] && (a = 0)),
                            a; )
                                try {
                                    if (n = 1,
                                    r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                    0) : r.next) && !(o = o.call(r, l[1])).done)
                                        return o;
                                    switch (r = 0,
                                    o && (l = [2 & l[0], o.value]),
                                    l[0]) {
                                    case 0:
                                    case 1:
                                        o = l;
                                        break;
                                    case 4:
                                        return a.label++,
                                        {
                                            value: l[1],
                                            done: !1
                                        };
                                    case 5:
                                        a.label++,
                                        r = l[1],
                                        l = [0];
                                        continue;
                                    case 7:
                                        l = a.ops.pop(),
                                        a.trys.pop();
                                        continue;
                                    default:
                                        if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                            a = 0;
                                            continue
                                        }
                                        if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                            a.label = l[1];
                                            break
                                        }
                                        if (6 === l[0] && a.label < o[1]) {
                                            a.label = o[1],
                                            o = l;
                                            break
                                        }
                                        if (o && a.label < o[2]) {
                                            a.label = o[2],
                                            a.ops.push(l);
                                            break
                                        }
                                        o[2] && a.ops.pop(),
                                        a.trys.pop();
                                        continue
                                    }
                                    l = t.call(e, a)
                                } catch (e) {
                                    l = [6, e],
                                    r = 0
                                } finally {
                                    n = o = 0
                                }
                            if (5 & l[0])
                                throw l[1];
                            return {
                                value: l[0] ? l[1] : void 0,
                                done: !0
                            }
                        }([l, c])
                    }
                }
            }
              , Et = function(e, t, n) {
                if (n || 2 === arguments.length)
                    for (var r, o = 0, a = t.length; o < a; o++)
                        !r && o in t || (r || (r = Array.prototype.slice.call(t, 0, o)),
                        r[o] = t[o]);
                return e.concat(r || Array.prototype.slice.call(t))
            };
            function jt() {
                var e = this
                  , t = (0,
                o.useState)()
                  , n = t[0]
                  , r = t[1]
                  , a = (0,
                o.useState)(!1)
                  , i = a[0]
                  , l = a[1]
                  , c = (0,
                o.useState)([])
                  , u = c[0]
                  , d = c[1]
                  , p = (0,
                o.useState)(0)
                  , v = p[0]
                  , m = p[1]
                  , h = (0,
                o.useState)(!1)
                  , g = h[0]
                  , b = h[1]
                  , _ = (0,
                o.useRef)()
                  , E = (0,
                o.useRef)(1)
                  , w = (0,
                o.useRef)(0)
                  , k = (0,
                Ye.fp)(mt.lB)
                  , S = k[0]
                  , O = k[1]
                  , N = function() {
                    return bt(e, void 0, void 0, function() {
                        var e;
                        return _t(this, function(t) {
                            switch (t.label) {
                            case 0:
                                return [4, tt({})];
                            case 1:
                                return e = t.sent().unreadMsgNum,
                                O(null != e ? e : 0),
                                [2]
                            }
                        })
                    })
                }
                  , C = (0,
                pt.A)()
                  , R = C.typeList
                  , x = C.getMessageTypeListAndUnreadNum
                  , P = C.readMsgAndRefreshRedDot
                  , A = function(e) {
                    var t = e.type || n || "";
                    return b(!0),
                    (0,
                    vt.n)({
                        groupCode: t,
                        pageSize: Qe.Q2,
                        pageNum: E.current,
                        readStatus: Qe.RJ.UnRead
                    }).then(function(e) {
                        return E.current++,
                        m(e.totalPage),
                        b(!1),
                        e
                    }).catch(function(e) {
                        (0,
                        f.r)()(e),
                        b(!1)
                    })
                };
                (0,
                o.useEffect)(function() {
                    return N(),
                    document.addEventListener("visibilitychange", function() {
                        "visible" === document.visibilityState && l(function(e) {
                            return e ? x() : N(),
                            e
                        })
                    }),
                    function() {
                        document.removeEventListener("visibilitychange", function() {
                            "visible" === document.visibilityState && l(function(e) {
                                return e ? x() : N(),
                                e
                            })
                        })
                    }
                }, []),
                (0,
                o.useEffect)(function() {
                    i ? bt(e, void 0, void 0, function() {
                        var e, t, n, o;
                        return _t(this, function(a) {
                            switch (a.label) {
                            case 0:
                                return a.trys.push([0, 2, , 3]),
                                [4, x()];
                            case 1:
                                return e = a.sent(),
                                (t = (null === (o = null == e ? void 0 : e[0]) || void 0 === o ? void 0 : o.groupCode) || void 0) && r(t),
                                [3, 3];
                            case 2:
                                return n = a.sent(),
                                (0,
                                f.r)()(n),
                                [3, 3];
                            case 3:
                                return [2]
                            }
                        })
                    }) : r(void 0)
                }, [i]),
                (0,
                o.useEffect)(function() {
                    var e;
                    n && (E.current = 1,
                    w.current = 0,
                    null === (e = _.current) || void 0 === e || e.scrollToItem(0, "start"),
                    A({}).then(function(e) {
                        d((null == e ? void 0 : e.dataList) || [])
                    }))
                }, [n]);
                var T = function(e, t) {
                    return function() {
                        window.open(s().stringifyUrl({
                            url: y.h.message,
                            query: {
                                group: n,
                                pageNum: Math.floor(t / Qe.Q2) + 1,
                                msgId: e
                            }
                        })),
                        l(!1)
                    }
                }
                  , L = function(t) {
                    return function() {
                        return bt(e, void 0, void 0, function() {
                            return _t(this, function(e) {
                                switch (e.label) {
                                case 0:
                                    return [4, P({
                                        msgId: t
                                    })];
                                case 1:
                                    return e.sent(),
                                    d(function(e) {
                                        return e.filter(function(e) {
                                            return e.id !== t
                                        })
                                    }),
                                    [2]
                                }
                            })
                        })
                    }
                };
                return o.createElement("div", {
                    className: "main_container__C3AYv"
                }, o.createElement(j.Ay, {
                    width: 600,
                    portalPadding: 0,
                    visible: i,
                    title: o.createElement("div", {
                        className: "main_header__gK1eP"
                    }, o.createElement("div", null, "未读信息"), o.createElement(V.A, {
                        customStyle: {
                            color: "#0071F3"
                        },
                        type: "textPrimary",
                        size: "small",
                        onClick: function() {
                            window.open(y.h.message),
                            l(!1)
                        },
                        "data-tracking-id": "Z2yXLuYwKLbY41yM"
                    }, o.createElement("span", {
                        style: {
                            fontWeight: 400
                        }
                    }, "全部"), o.createElement(I.default, {
                        type: "right",
                        marginLeft: 4,
                        fontSize: 12
                    }))),
                    content: o.createElement("div", {
                        className: "main_content__nmB9x"
                    }, o.createElement("div", {
                        className: "main_type__Rif6Z"
                    }, o.createElement(ht.default, {
                        tabPosition: "left",
                        customStyle: {
                            tabWidth: 128
                        },
                        activeKey: n,
                        onChange: function(e) {
                            r(e)
                        },
                        customTabItem: function(e, t) {
                            var r;
                            return o.createElement("div", {
                                className: at()("main_typeItemWrapper__GczXF", (r = {},
                                r.main_typeItemActive__nVm7U = n === t.key,
                                r))
                            }, o.createElement(it.A, {
                                key: t.key,
                                count: t.unreadNum || 0
                            }, e))
                        },
                        dataSource: R.map(function(e) {
                            return {
                                key: e.groupCode,
                                label: e.groupDesc,
                                unreadNum: e.unreadNum
                            }
                        })
                    })), o.createElement("div", {
                        className: "main_list__SEpis"
                    }, u.length > 0 ? o.createElement(yt.default, {
                        ref: _,
                        height: 350,
                        itemCount: u.length,
                        onScroll: function(e, t, n, r) {
                            "next" === r && n < 160 && !g && v >= E.current && A({}).then(function(e) {
                                d(function(t) {
                                    return Et(Et([], t, !0), (null == e ? void 0 : e.dataList) || [], !0)
                                })
                            }),
                            w.current = e
                        },
                        renderItem: function(e) {
                            var t = u[e];
                            return t ? o.createElement(dt, {
                                key: t.id,
                                data: t,
                                onClick: T(t.id, e),
                                onReadClick: L(t.id),
                                onJumpUrlClick: function() {
                                    L(t.id)(),
                                    setTimeout(function() {
                                        l(!1)
                                    })
                                }
                            }) : null
                        }
                    }) : o.createElement(ft.A, {
                        title: "暂无未读信息",
                        height: 300
                    }))),
                    onVisibleChange: l
                }, o.createElement(gt.default, {
                    count: S,
                    top: -6,
                    right: 1
                }, o.createElement("div", {
                    className: "main_message__bPbX2"
                }, o.createElement("div", {
                    className: "main_icon__zrBKZ"
                }), "消息中心"))))
            }
            function It() {
                var e = this
                  , t = (0,
                o.useRef)()
                  , r = (0,
                Ye.fp)(mt.lB)[1]
                  , a = function(t) {
                    return o = e,
                    a = void 0,
                    u = function() {
                        var e, o, a;
                        return function(e, t) {
                            var n, r, o, a = {
                                label: 0,
                                sent: function() {
                                    if (1 & o[0])
                                        throw o[1];
                                    return o[1]
                                },
                                trys: [],
                                ops: []
                            }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                            return i.next = l(0),
                            i.throw = l(1),
                            i.return = l(2),
                            "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                return this
                            }
                            ),
                            i;
                            function l(l) {
                                return function(c) {
                                    return function(l) {
                                        if (n)
                                            throw new TypeError("Generator is already executing.");
                                        for (; i && (i = 0,
                                        l[0] && (a = 0)),
                                        a; )
                                            try {
                                                if (n = 1,
                                                r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                                0) : r.next) && !(o = o.call(r, l[1])).done)
                                                    return o;
                                                switch (r = 0,
                                                o && (l = [2 & l[0], o.value]),
                                                l[0]) {
                                                case 0:
                                                case 1:
                                                    o = l;
                                                    break;
                                                case 4:
                                                    return a.label++,
                                                    {
                                                        value: l[1],
                                                        done: !1
                                                    };
                                                case 5:
                                                    a.label++,
                                                    r = l[1],
                                                    l = [0];
                                                    continue;
                                                case 7:
                                                    l = a.ops.pop(),
                                                    a.trys.pop();
                                                    continue;
                                                default:
                                                    if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                                        a = 0;
                                                        continue
                                                    }
                                                    if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                                        a.label = l[1];
                                                        break
                                                    }
                                                    if (6 === l[0] && a.label < o[1]) {
                                                        a.label = o[1],
                                                        o = l;
                                                        break
                                                    }
                                                    if (o && a.label < o[2]) {
                                                        a.label = o[2],
                                                        a.ops.push(l);
                                                        break
                                                    }
                                                    o[2] && a.ops.pop(),
                                                    a.trys.pop();
                                                    continue
                                                }
                                                l = t.call(e, a)
                                            } catch (e) {
                                                l = [6, e],
                                                r = 0
                                            } finally {
                                                n = o = 0
                                            }
                                        if (5 & l[0])
                                            throw l[1];
                                        return {
                                            value: l[0] ? l[1] : void 0,
                                            done: !0
                                        }
                                    }([l, c])
                                }
                            }
                        }(this, function(c) {
                            switch (c.label) {
                            case 0:
                                return [4, (u = {},
                                (0,
                                P.b)(et.Response, "/bg/quick/api/kj/merchant/msgBox/unreadMsgDetail", u, void 0))];
                            case 1:
                                return e = c.sent().unreadPopMsg,
                                t ? [3, 3] : [4, tt({})];
                            case 2:
                                (o = c.sent().unreadMsgNum) && r(o),
                                c.label = 3;
                            case 3:
                                return a = null == e ? void 0 : e.filter(function(e) {
                                    var t = e.readStatus
                                      , n = e.msgActionType;
                                    return t === Qe.RJ.UnRead && n === Qe.X2.Modal
                                }),
                                (null == a ? void 0 : a.length) && (l(),
                                Promise.all([n.e(543), n.e(218), n.e(372), n.e(179)]).then(n.bind(n, 86644)).then(function(e) {
                                    (0,
                                    e.default)({
                                        forceRead: !0,
                                        dataSource: a,
                                        onClose: function() {
                                            return i(!0)
                                        }
                                    })
                                })),
                                [2]
                            }
                            var u
                        })
                    }
                    ,
                    new ((c = void 0) || (c = Promise))(function(e, t) {
                        function n(e) {
                            try {
                                i(u.next(e))
                            } catch (e) {
                                t(e)
                            }
                        }
                        function r(e) {
                            try {
                                i(u.throw(e))
                            } catch (e) {
                                t(e)
                            }
                        }
                        function i(t) {
                            var o;
                            t.done ? e(t.value) : (o = t.value,
                            o instanceof c ? o : new c(function(e) {
                                e(o)
                            }
                            )).then(n, r)
                        }
                        i((u = u.apply(o, a || [])).next())
                    }
                    );
                    var o, a, c, u
                }
                  , i = function(e) {
                    t.current = setInterval(function() {
                        return a()
                    }, 3e5),
                    e && a()
                }
                  , l = function() {
                    return t.current && clearInterval(t.current)
                };
                return (0,
                o.useEffect)(function() {
                    return "1" !== (0,
                    Ge.P)("skp") && a(!0),
                    i(),
                    function() {
                        return l()
                    }
                }, []),
                o.createElement(jt, null)
            }
            var wt = n(99193);
            const kt = function() {
                var e = (0,
                l.W6)();
                return o.createElement(wt.default, {
                    borderRadius: "6px",
                    backgroundColor: "rgba(0,0,0,0.04)",
                    padding: "0 36px 2px 36px",
                    marginLeft: 36
                }, o.createElement(ht.default, {
                    type: "line",
                    activeKey: [y.h.graphService, y.h.qualificationService].includes(location.pathname) ? location.pathname : [y.h.orderDetail, y.h.orderList].includes(location.pathname) ? y.h.qualificationService : void 0,
                    onChange: function(t) {
                        e.push(t)
                    },
                    dataSource: [{
                        label: "合规服务",
                        key: y.h.qualificationService
                    }, {
                        label: "摄影服务",
                        key: y.h.graphService
                    }]
                }))
            }
              , St = n.p + "e62e922cee085973cde2.png";
            var Ot, Nt = n(4447);
            !function(e) {
                e.Res = {};
                var t = function() {
                    function e() {}
                    return function(e, t, n, r) {
                        var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                        if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                            i = Reflect.decorate(e, t, n, r);
                        else
                            for (var l = e.length - 1; l >= 0; l--)
                                (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                        a > 3 && i && Object.defineProperty(t, n, i)
                    }([(0,
                    C.X)()], e.prototype, "res", void 0),
                    e
                }();
                e.Response = t
            }(Ot || (Ot = {}));
            const Ct = "user-info_mallLogo__ASx0u"
              , Rt = "user-info_img__CXs-y"
              , xt = "user-info_mallName__hDgOI"
              , Pt = "user-info_mallInfo__4QNdV";
            var At = X.default.alert
              , Tt = function(e) {
                var t = e.userInfo
                  , n = e.isEntity
                  , r = null == t ? void 0 : t.maskMobile
                  , a = (0,
                o.useState)(!1)
                  , i = a[0]
                  , l = a[1]
                  , c = function() {
                    return e = void 0,
                    t = void 0,
                    r = function() {
                        var e;
                        return function(e, t) {
                            var n, r, o, a = {
                                label: 0,
                                sent: function() {
                                    if (1 & o[0])
                                        throw o[1];
                                    return o[1]
                                },
                                trys: [],
                                ops: []
                            }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                            return i.next = l(0),
                            i.throw = l(1),
                            i.return = l(2),
                            "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                return this
                            }
                            ),
                            i;
                            function l(l) {
                                return function(c) {
                                    return function(l) {
                                        if (n)
                                            throw new TypeError("Generator is already executing.");
                                        for (; i && (i = 0,
                                        l[0] && (a = 0)),
                                        a; )
                                            try {
                                                if (n = 1,
                                                r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                                0) : r.next) && !(o = o.call(r, l[1])).done)
                                                    return o;
                                                switch (r = 0,
                                                o && (l = [2 & l[0], o.value]),
                                                l[0]) {
                                                case 0:
                                                case 1:
                                                    o = l;
                                                    break;
                                                case 4:
                                                    return a.label++,
                                                    {
                                                        value: l[1],
                                                        done: !1
                                                    };
                                                case 5:
                                                    a.label++,
                                                    r = l[1],
                                                    l = [0];
                                                    continue;
                                                case 7:
                                                    l = a.ops.pop(),
                                                    a.trys.pop();
                                                    continue;
                                                default:
                                                    if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                                        a = 0;
                                                        continue
                                                    }
                                                    if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                                        a.label = l[1];
                                                        break
                                                    }
                                                    if (6 === l[0] && a.label < o[1]) {
                                                        a.label = o[1],
                                                        o = l;
                                                        break
                                                    }
                                                    if (o && a.label < o[2]) {
                                                        a.label = o[2],
                                                        a.ops.push(l);
                                                        break
                                                    }
                                                    o[2] && a.ops.pop(),
                                                    a.trys.pop();
                                                    continue
                                                }
                                                l = t.call(e, a)
                                            } catch (e) {
                                                l = [6, e],
                                                r = 0
                                            } finally {
                                                n = o = 0
                                            }
                                        if (5 & l[0])
                                            throw l[1];
                                        return {
                                            value: l[0] ? l[1] : void 0,
                                            done: !0
                                        }
                                    }([l, c])
                                }
                            }
                        }(this, function(t) {
                            switch (t.label) {
                            case 0:
                                return t.trys.push([0, 2, , 3]),
                                [4, (n = {},
                                (0,
                                Q.IM)("/bg/quiet/api/mms/logout", n))];
                            case 1:
                                return t.sent(),
                                window.location.href = "".concat(window.location.origin, "/login"),
                                [3, 3];
                            case 2:
                                return e = t.sent(),
                                (0,
                                f.r)()(e),
                                [3, 3];
                            case 3:
                                return [2]
                            }
                            var n
                        })
                    }
                    ,
                    new ((n = void 0) || (n = Promise))(function(o, a) {
                        function i(e) {
                            try {
                                c(r.next(e))
                            } catch (e) {
                                a(e)
                            }
                        }
                        function l(e) {
                            try {
                                c(r.throw(e))
                            } catch (e) {
                                a(e)
                            }
                        }
                        function c(e) {
                            var t;
                            e.done ? o(e.value) : (t = e.value,
                            t instanceof n ? t : new n(function(e) {
                                e(t)
                            }
                            )).then(i, l)
                        }
                        c((r = r.apply(e, t || [])).next())
                    }
                    );
                    var e, t, n, r
                };
                return o.createElement(o.Fragment, null, o.createElement(Nt.default, {
                    placement: "bottom",
                    trigger: "click",
                    visible: i,
                    onVisibleChange: function() {
                        return l(!i)
                    },
                    content: o.createElement("div", {
                        className: "user-info_accountSection__iMWXQ"
                    }, o.createElement("div", {
                        className: "user-info_mainMall__ZXMxz"
                    }, o.createElement("div", {
                        className: Ct
                    }, o.createElement("div", {
                        className: Rt
                    })), o.createElement("div", {
                        className: Pt
                    }, o.createElement("div", {
                        className: xt
                    }, r))), o.createElement("div", {
                        className: "user-info_btnGroup__cjaTo"
                    }, o.createElement("div", {
                        className: "user-info_btnItem__J6jvc",
                        onClick: function() {
                            At({
                                content: o.createElement("div", {
                                    style: {
                                        fontWeight: "bold"
                                    }
                                }, o.createElement(I.default, {
                                    type: "warning-circle_filled",
                                    color: "#FF6800",
                                    margin: "0 6px 0 0 "
                                }), "确认退出账号吗？"),
                                footerAlign: "right",
                                onOk: c
                            })
                        }
                    }, o.createElement(I.default, {
                        type: "export",
                        marginRight: 8
                    }), "退出登录"))),
                    withArrow: !1,
                    portalPadding: 0,
                    topOffset: 20
                }, o.createElement("div", {
                    className: "user-info_accountInfo__Md5rS"
                }, o.createElement("div", {
                    className: Ct
                }, o.createElement("div", {
                    className: Rt,
                    style: {
                        backgroundImage: n ? "url('".concat(St, "')") : void 0
                    }
                })), o.createElement("div", {
                    className: Pt,
                    onClick: function() {
                        return l(!i)
                    }
                }, o.createElement("div", {
                    className: xt
                }, r)), o.createElement("div", {
                    className: "user-info_suffix__SE1Wt"
                }, o.createElement(I.default, {
                    type: i ? "up" : "down"
                })))))
            };
            const Lt = "entity-info-header_icon__ay8b6"
              , Mt = function(e) {
                var t, n, r, a = e.userInfo, i = (0,
                l.zy)(), c = null === (t = [y.h.mallInfoEntry, y.h.mallMallEntry]) || void 0 === t ? void 0 : t.find(function(e) {
                    var t;
                    return -1 !== (null === (t = null == i ? void 0 : i.pathname) || void 0 === t ? void 0 : t.indexOf(e))
                }), u = null === (n = [y.h.mainSitePage, y.h.ruleCenter]) || void 0 === n ? void 0 : n.find(function(e) {
                    var t;
                    return -1 !== (null === (t = null == i ? void 0 : i.pathname) || void 0 === t ? void 0 : t.indexOf(e))
                }), s = null === (r = [y.h.graphService, y.h.qualificationService, y.h.orderDetail, y.h.orderList]) || void 0 === r ? void 0 : r.find(function(e) {
                    var t;
                    return -1 !== (null === (t = null == i ? void 0 : i.pathname) || void 0 === t ? void 0 : t.indexOf(e))
                });
                return o.createElement("div", {
                    className: "entity-info-header_headerContainer__h6bJJ",
                    style: {
                        height: c ? 56 : void 0
                    }
                }, o.createElement("div", {
                    className: "entity-info-header_systemInfo__JyVg2"
                }, s ? o.createElement(o.Fragment, null, o.createElement("div", {
                    className: "entity-info-header_serviceMarketLogo__jdQ0P"
                }), o.createElement(kt, null)) : o.createElement(o.Fragment, null, c ? o.createElement("div", {
                    className: "entity-info-header_temuLogo__dujdK"
                }) : o.createElement(o.Fragment, null, o.createElement("div", {
                    className: "entity-info-header_tLogo__p4FA9"
                }), o.createElement("div", {
                    className: "entity-info-header_logo__q5kOz"
                })), o.createElement("span", {
                    className: "entity-info-header_tag__2QhlI"
                }, "Beta"))), o.createElement("div", {
                    className: "entity-info-header_userInfo__TRfyZ"
                }, s && o.createElement("div", {
                    className: "entity-info-header_sell__YrZTo",
                    onClick: function() {
                        return (0,
                        b.CC)(y.h.mainSitePage)
                    }
                }, "卖家中心"), u && o.createElement("div", {
                    className: "entity-info-header_sectionGroup__CJbDg"
                }, (null == a ? void 0 : a.accountType) === _.CO.MainAccount && (null == a ? void 0 : a.accountStatus) !== _.u1.Ban && o.createElement("div", {
                    className: "entity-info-header_account__pdu12",
                    onClick: function() {
                        return (0,
                        b.CC)(y.h.mallAccountInfo)
                    }
                }, o.createElement("div", {
                    className: Lt
                }), "账户管理"), o.createElement("div", {
                    className: "entity-info-header_rule__izVFp",
                    onClick: function() {
                        return (0,
                        b.CC)("/settle/rule-center")
                    }
                }, o.createElement("div", {
                    className: Lt
                }), "规则中心"), o.createElement("div", {
                    className: "entity-info-header_course__pA9Td",
                    onClick: function() {
                        return (0,
                        b.CC)("/main/course/list")
                    }
                }, o.createElement("div", {
                    className: Lt
                }), "卖家课堂"), o.createElement(It, null), !s && o.createElement(Je, null)), c && o.createElement(Je, null), o.createElement(Tt, {
                    userInfo: a,
                    isEntity: !0
                })))
            };
            var zt = n(61775)
              , Ut = function() {
                return Ut = Object.assign || function(e) {
                    for (var t, n = 1, r = arguments.length; n < r; n++)
                        for (var o in t = arguments[n])
                            Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
                    return e
                }
                ,
                Ut.apply(this, arguments)
            }
              , Bt = function(e, t, n, r) {
                return new (n || (n = Promise))(function(o, a) {
                    function i(e) {
                        try {
                            c(r.next(e))
                        } catch (e) {
                            a(e)
                        }
                    }
                    function l(e) {
                        try {
                            c(r.throw(e))
                        } catch (e) {
                            a(e)
                        }
                    }
                    function c(e) {
                        var t;
                        e.done ? o(e.value) : (t = e.value,
                        t instanceof n ? t : new n(function(e) {
                            e(t)
                        }
                        )).then(i, l)
                    }
                    c((r = r.apply(e, t || [])).next())
                }
                )
            }
              , Dt = function(e, t) {
                var n, r, o, a = {
                    label: 0,
                    sent: function() {
                        if (1 & o[0])
                            throw o[1];
                        return o[1]
                    },
                    trys: [],
                    ops: []
                }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                return i.next = l(0),
                i.throw = l(1),
                i.return = l(2),
                "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                    return this
                }
                ),
                i;
                function l(l) {
                    return function(c) {
                        return function(l) {
                            if (n)
                                throw new TypeError("Generator is already executing.");
                            for (; i && (i = 0,
                            l[0] && (a = 0)),
                            a; )
                                try {
                                    if (n = 1,
                                    r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                    0) : r.next) && !(o = o.call(r, l[1])).done)
                                        return o;
                                    switch (r = 0,
                                    o && (l = [2 & l[0], o.value]),
                                    l[0]) {
                                    case 0:
                                    case 1:
                                        o = l;
                                        break;
                                    case 4:
                                        return a.label++,
                                        {
                                            value: l[1],
                                            done: !1
                                        };
                                    case 5:
                                        a.label++,
                                        r = l[1],
                                        l = [0];
                                        continue;
                                    case 7:
                                        l = a.ops.pop(),
                                        a.trys.pop();
                                        continue;
                                    default:
                                        if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                            a = 0;
                                            continue
                                        }
                                        if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                            a.label = l[1];
                                            break
                                        }
                                        if (6 === l[0] && a.label < o[1]) {
                                            a.label = o[1],
                                            o = l;
                                            break
                                        }
                                        if (o && a.label < o[2]) {
                                            a.label = o[2],
                                            a.ops.push(l);
                                            break
                                        }
                                        o[2] && a.ops.pop(),
                                        a.trys.pop();
                                        continue
                                    }
                                    l = t.call(e, a)
                                } catch (e) {
                                    l = [6, e],
                                    r = 0
                                } finally {
                                    n = o = 0
                                }
                            if (5 & l[0])
                                throw l[1];
                            return {
                                value: l[0] ? l[1] : void 0,
                                done: !0
                            }
                        }([l, c])
                    }
                }
            }
              , Kt = (0,
            o.createContext)(void 0)
              , Ft = [y.h.entityRelate, y.h.relateAuthorization]
              , Vt = function(e) {
                var t, n = e.userInfo;
                return (null == (t = Ft) ? void 0 : t.find(function(e) {
                    var t;
                    return -1 !== (null === (t = null === location || void 0 === location ? void 0 : location.pathname) || void 0 === t ? void 0 : t.indexOf(e))
                })) ? null : o.createElement(Mt, {
                    userInfo: n
                })
            }
              , Xt = function() {
                var e = (0,
                o.useMemo)(function() {
                    return o.createElement(l.dO, null, d.A.map(function(e) {
                        var t = i()({
                            loader: e.load,
                            loading: function() {
                                return o.createElement(m.B, null)
                            },
                            render: function(t, n) {
                                var r, a = t.default;
                                return o.createElement(g.Qc, {
                                    title: null !== (r = null == e ? void 0 : e.title) && void 0 !== r ? r : ""
                                }, o.createElement(a, Ut({}, n)))
                            }
                        });
                        return o.createElement(l.qh, {
                            exact: !0,
                            key: e.path,
                            path: e.path,
                            component: function() {
                                return o.createElement(t, null)
                            }
                        })
                    }), o.createElement(l.qh, {
                        path: "/"
                    }, o.createElement(l.rd, {
                        to: "/settle/site-main"
                    })))
                }, [d.A]);
                return o.createElement(o.Fragment, null, e)
            };
            const Ht = function() {
                var e = this
                  , t = (0,
                l.W6)()
                  , n = (0,
                l.zy)()
                  , r = null == Ft ? void 0 : Ft.find(function(e) {
                    var t;
                    return -1 !== (null === (t = null == n ? void 0 : n.pathname) || void 0 === t ? void 0 : t.indexOf(e))
                })
                  , a = (0,
                o.useState)()
                  , i = a[0]
                  , u = a[1]
                  , d = (0,
                o.useState)(!0)
                  , g = d[0]
                  , b = d[1]
                  , _ = r ? 0 : 72;
                (0,
                o.useEffect)(function() {
                    (0,
                    p.M)({}).then(function(t) {
                        return Bt(e, void 0, void 0, function() {
                            var e, n, r;
                            return Dt(this, function(o) {
                                switch (o.label) {
                                case 0:
                                    return u(t),
                                    (0,
                                    v.P)(null === (n = null == t ? void 0 : t.userId) || void 0 === n ? void 0 : n.toString()),
                                    zt.A.init({
                                        text: "".concat(null !== (r = null == t ? void 0 : t.userId) && void 0 !== r ? r : ""),
                                        font: "16px PingFang SC",
                                        fillStyle: "rgba(100,100,100,0.08)",
                                        width: 140,
                                        height: 140,
                                        isInternet: !0
                                    }),
                                    [4, p.A.getMallIdAsync()];
                                case 1:
                                    return e = o.sent(),
                                    (0,
                                    c.OB)({
                                        userId: null == t ? void 0 : t.userId,
                                        mallId: e
                                    }),
                                    [4, E()];
                                case 2:
                                    return o.sent(),
                                    [2]
                                }
                            })
                        })
                    }).catch(function(e) {
                        (0,
                        v.P)(""),
                        (0,
                        f.r)()(e)
                    })
                }, []);
                var E = function() {
                    return Bt(e, void 0, void 0, function() {
                        var e, n, r, o;
                        return Dt(this, function(a) {
                            switch (a.label) {
                            case 0:
                                return a.trys.push([0, 2, 3, 4]),
                                e = s().parse(window.location.search) || {},
                                "mallInfo" === (n = e.ignoreCheck) || "authenticationInfo" === n ? [2] : [4, (0,
                                h.S)({
                                    settleSiteSource: 0
                                })];
                            case 1:
                                return 1 === (r = a.sent()).sellerFinderStrategy || 2 === r.sellerFinderStrategy || 3 === r.sellerFinderStrategy || 5 === r.sellerFinderStrategy ? (t.replace(y.h.mallEnterpriseEntry),
                                [2]) : [3, 4];
                            case 2:
                                return o = a.sent(),
                                (0,
                                f.r)()(o),
                                [3, 4];
                            case 3:
                                return b(!1),
                                [7];
                            case 4:
                                return [2]
                            }
                        })
                    })
                };
                return o.createElement("div", {
                    className: "layout_pageContainer__oIxPu"
                }, o.createElement(Vt, {
                    userInfo: i
                }), o.createElement("div", {
                    className: "layout_bodyContainer__fraMW",
                    style: {
                        height: "calc(100vh - ".concat(_, "px)")
                    }
                }, g ? o.createElement("div", {
                    style: {
                        display: "flex",
                        width: "100%"
                    }
                }, o.createElement(m.B, {
                    height: "calc(100vh - ".concat(_, "px)")
                })) : o.createElement(Kt.Provider, {
                    value: i
                }, o.createElement(Xt, null))))
            }
        }
        ,
        31022: (e, t, n) => {
            "use strict";
            n.d(t, {
                Qc: () => y,
                Ay: () => _
            });
            var r = n(96540)
              , o = n(53259)
              , a = n.n(o)
              , i = n(54625)
              , l = n(24180)
              , c = n(73504)
              , u = n(54839)
              , s = n(98959)
              , d = {
                restoreOnUnmount: !1
            };
            const f = "undefined" != typeof document ? function(e, t) {
                void 0 === t && (t = d);
                var n = (0,
                r.useRef)(document.title);
                try {
                    document.title !== e && (document.title = e)
                } catch (e) {
                    console.error("set title error")
                }
                (0,
                r.useEffect)(function() {
                    return t && t.restoreOnUnmount ? function() {
                        document.title = n.current
                    }
                    : void 0
                }, [])
            }
            : function(e) {}
            ;
            var p = n(33652)
              , v = n(24368)
              , m = (n(77204),
            n(91867),
            n(30760))
              , h = n(35097)
              , y = function(e) {
                var t = e.children
                  , n = e.title;
                return f(n),
                r.createElement(r.Fragment, null, t)
            }
              , g = function(e) {
                var t = e.children;
                return (0,
                r.useEffect)(function() {
                    (0,
                    s.P)("")
                }, []),
                r.createElement(r.Fragment, null, t)
            }
              , b = p.t.reduce(function(e, t) {
                return e[t.path] = a()({
                    loader: t.load,
                    loading: function() {
                        return r.createElement("div", {
                            style: {
                                minHeight: 350
                            }
                        })
                    },
                    render: function(e) {
                        var n, o = e.default;
                        return r.createElement(g, null, r.createElement(y, {
                            title: null !== (n = null == t ? void 0 : t.title) && void 0 !== n ? n : ""
                        }, r.createElement(o, null)))
                    }
                }),
                e
            }, {});
            const _ = function() {
                return (0,
                r.useEffect)(function() {
                    (0,
                    m.br)(!1)
                }, []),
                r.createElement(u.Ay, null, r.createElement(c.A, null), r.createElement(i.Kd, null, r.createElement(l.dO, null, p.t.map(function(e) {
                    return r.createElement(l.qh, {
                        exact: !0,
                        key: e.path,
                        path: e.path,
                        component: b[e.path]
                    })
                }), r.createElement(l.qh, null, r.createElement(h.default, {
                    mode: "light"
                }, r.createElement(v.A, null))))))
            }
        }
        ,
        31784: (e, t, n) => {
            "use strict";
            n.d(t, {
                $z: () => o,
                DW: () => a,
                Ln: () => r
            }),
            n(75657);
            var r = function() {
                var e;
                return null === (e = window.location) || void 0 === e ? void 0 : e.host.replace(/^dev\./, "")
            }
              , o = function() {
                return "".concat(location.protocol, "//").concat(r())
            }
              , a = function() {
                return "file.kuajingmaihuo.com"
            }
        }
        ,
        33371: (e, t, n) => {
            "use strict";
            n.d(t, {
                CC: () => i,
                Fh: () => c,
                fU: () => l
            });
            var r = n(74353)
              , o = n.n(r)
              , a = n(97859)
              , i = function(e) {
                void 0 === e && (e = "");
                var t = document.createElement("a");
                t.setAttribute("href", e),
                t.setAttribute("target", "_blank"),
                t.click()
            }
              , l = function(e, t) {
                return void 0 === t && (t = "YYYY-MM-DD HH:mm:ss"),
                e ? o()(e).format(t) : "-"
            }
              , c = function(e) {
                if ((0,
                a.A)(e))
                    return !1;
                try {
                    var t = JSON.parse(e);
                    return !("object" != typeof t || !t)
                } catch (e) {
                    return !1
                }
            }
        }
        ,
        33652: (e, t, n) => {
            "use strict";
            n.d(t, {
                A: () => i,
                t: () => a
            });
            var r = n(20195)
              , o = [{
                path: r.hR.mallEnterpriseEntry,
                load: function() {
                    return Promise.all([n.e(93), n.e(424), n.e(309), n.e(194), n.e(218), n.e(612), n.e(605), n.e(113)]).then(n.bind(n, 28335))
                },
                title: "卖家中心"
            }, {
                path: r.hR.mainSitePage,
                load: function() {
                    return Promise.all([n.e(91), n.e(93), n.e(194), n.e(125), n.e(674), n.e(44)]).then(n.bind(n, 24642))
                },
                title: "卖家中心"
            }, {
                path: r.hR.siteTrusteeship,
                load: function() {
                    return n.e(683).then(n.bind(n, 30524))
                },
                title: "卖家中心"
            }, {
                path: r.hR.ruleCenter,
                load: function() {
                    return Promise.all([n.e(93), n.e(173)]).then(n.bind(n, 53936))
                },
                title: "卖家中心"
            }, {
                path: r.hR.message,
                load: function() {
                    return Promise.all([n.e(543), n.e(93), n.e(218), n.e(372), n.e(98)]).then(n.bind(n, 61278))
                },
                title: "卖家中心"
            }, {
                path: r.hR.graphService,
                load: function() {
                    return n.e(77).then(n.bind(n, 40833))
                },
                title: "卖家中心"
            }, {
                path: r.hR.qualificationService,
                load: function() {
                    return n.e(286).then(n.bind(n, 96912))
                },
                title: "卖家中心"
            }, {
                path: r.hR.orderDetail,
                load: function() {
                    return n.e(221).then(n.bind(n, 19334))
                },
                title: "卖家中心"
            }, {
                path: r.hR.orderList,
                load: function() {
                    return n.e(662).then(n.bind(n, 45276))
                },
                title: "卖家中心"
            }, {
                path: r.hR.message,
                load: function() {
                    return Promise.all([n.e(543), n.e(93), n.e(218), n.e(372), n.e(98)]).then(n.bind(n, 61278))
                },
                title: "卖家中心"
            }, {
                path: r.hR.entityRelate,
                load: function() {
                    return Promise.all([n.e(424), n.e(309), n.e(194), n.e(879), n.e(300)]).then(n.bind(n, 82679))
                },
                title: "卖家中心"
            }, {
                path: r.hR.mallTransfer,
                load: function() {
                    return Promise.all([n.e(93), n.e(424), n.e(309), n.e(499)]).then(n.bind(n, 8696))
                },
                title: "卖家中心"
            }, {
                path: r.hR.relateAuthorization,
                load: function() {
                    return Promise.all([n.e(879), n.e(28)]).then(n.bind(n, 25629))
                },
                title: "卖家中心"
            }]
              , a = [{
                path: r.hR.loginHosting,
                load: function() {
                    return n.e(51).then(n.bind(n, 7385))
                },
                title: "卖家中心"
            }, {
                path: r.hR.login,
                load: function() {
                    return Promise.all([n.e(543), n.e(91), n.e(93), n.e(424), n.e(940), n.e(379), n.e(966)]).then(n.bind(n, 56355))
                },
                title: "卖家中心"
            }, {
                path: r.hR.sellerLogin,
                load: function() {
                    return Promise.all([n.e(543), n.e(91), n.e(125), n.e(379), n.e(242)]).then(n.bind(n, 80279))
                },
                title: "卖家中心"
            }, {
                path: r.hR.activityLogin,
                load: function() {
                    return Promise.all([n.e(543), n.e(91), n.e(379), n.e(232)]).then(n.bind(n, 19341))
                },
                title: "卖家中心"
            }, {
                path: r.hR.loginRegister,
                load: function() {
                    return Promise.all([n.e(543), n.e(91), n.e(940), n.e(379), n.e(536)]).then(n.bind(n, 77824))
                },
                title: "卖家中心"
            }, {
                path: r.hR.loginMms,
                load: function() {
                    return Promise.all([n.e(91), n.e(605), n.e(396)]).then(n.bind(n, 6377))
                },
                title: "卖家中心"
            }, {
                path: "".concat(r.Ko).concat(r.hR.loginMms),
                load: function() {
                    return Promise.all([n.e(91), n.e(605), n.e(396)]).then(n.bind(n, 6377))
                },
                title: "卖家中心"
            }, {
                path: r.hR.mainLogin,
                load: function() {
                    return Promise.all([n.e(543), n.e(91), n.e(93), n.e(424), n.e(940), n.e(379), n.e(966)]).then(n.bind(n, 56355))
                },
                title: "卖家中心"
            }, {
                path: r.hR.mainLoginRegister,
                load: function() {
                    return Promise.all([n.e(543), n.e(91), n.e(940), n.e(379), n.e(536)]).then(n.bind(n, 77824))
                },
                title: "卖家中心"
            }, {
                path: r.hR.loginResetPassword,
                load: function() {
                    return Promise.all([n.e(543), n.e(91), n.e(93), n.e(309), n.e(379), n.e(450)]).then(n.bind(n, 1599))
                },
                title: "卖家中心"
            }, {
                path: r.hR.mainLoginResetPassword,
                load: function() {
                    return Promise.all([n.e(543), n.e(91), n.e(93), n.e(309), n.e(379), n.e(450)]).then(n.bind(n, 1599))
                },
                title: "卖家中心"
            }, {
                path: r.hR.mainLoginModifyMobile,
                load: function() {
                    return Promise.all([n.e(543), n.e(424), n.e(309), n.e(194), n.e(244)]).then(n.bind(n, 27724))
                },
                title: "卖家中心"
            }, {
                path: r.hR.loginModifyMobile,
                load: function() {
                    return Promise.all([n.e(543), n.e(424), n.e(309), n.e(194), n.e(244)]).then(n.bind(n, 27724))
                },
                title: "卖家中心"
            }];
            const i = o
        }
        ,
        35358: (e, t, n) => {
            var r = {
                "./af": 25177,
                "./af.js": 25177,
                "./ar": 61509,
                "./ar-dz": 41488,
                "./ar-dz.js": 41488,
                "./ar-kw": 58676,
                "./ar-kw.js": 58676,
                "./ar-ly": 42353,
                "./ar-ly.js": 42353,
                "./ar-ma": 24496,
                "./ar-ma.js": 24496,
                "./ar-ps": 6947,
                "./ar-ps.js": 6947,
                "./ar-sa": 82682,
                "./ar-sa.js": 82682,
                "./ar-tn": 89756,
                "./ar-tn.js": 89756,
                "./ar.js": 61509,
                "./az": 95533,
                "./az.js": 95533,
                "./be": 28959,
                "./be.js": 28959,
                "./bg": 47777,
                "./bg.js": 47777,
                "./bm": 54903,
                "./bm.js": 54903,
                "./bn": 61290,
                "./bn-bd": 17357,
                "./bn-bd.js": 17357,
                "./bn.js": 61290,
                "./bo": 31545,
                "./bo.js": 31545,
                "./br": 11470,
                "./br.js": 11470,
                "./bs": 44429,
                "./bs.js": 44429,
                "./ca": 7306,
                "./ca.js": 7306,
                "./cs": 56464,
                "./cs.js": 56464,
                "./cv": 73635,
                "./cv.js": 73635,
                "./cy": 64226,
                "./cy.js": 64226,
                "./da": 93601,
                "./da.js": 93601,
                "./de": 77853,
                "./de-at": 26111,
                "./de-at.js": 26111,
                "./de-ch": 54697,
                "./de-ch.js": 54697,
                "./de.js": 77853,
                "./dv": 60708,
                "./dv.js": 60708,
                "./el": 54691,
                "./el.js": 54691,
                "./en-au": 53872,
                "./en-au.js": 53872,
                "./en-ca": 28298,
                "./en-ca.js": 28298,
                "./en-gb": 56195,
                "./en-gb.js": 56195,
                "./en-ie": 66584,
                "./en-ie.js": 66584,
                "./en-il": 65543,
                "./en-il.js": 65543,
                "./en-in": 9033,
                "./en-in.js": 9033,
                "./en-nz": 79402,
                "./en-nz.js": 79402,
                "./en-sg": 43004,
                "./en-sg.js": 43004,
                "./eo": 32934,
                "./eo.js": 32934,
                "./es": 97650,
                "./es-do": 20838,
                "./es-do.js": 20838,
                "./es-mx": 17730,
                "./es-mx.js": 17730,
                "./es-us": 56575,
                "./es-us.js": 56575,
                "./es.js": 97650,
                "./et": 3035,
                "./et.js": 3035,
                "./eu": 3508,
                "./eu.js": 3508,
                "./fa": 119,
                "./fa.js": 119,
                "./fi": 90527,
                "./fi.js": 90527,
                "./fil": 95995,
                "./fil.js": 95995,
                "./fo": 52477,
                "./fo.js": 52477,
                "./fr": 85498,
                "./fr-ca": 26435,
                "./fr-ca.js": 26435,
                "./fr-ch": 37892,
                "./fr-ch.js": 37892,
                "./fr.js": 85498,
                "./fy": 37071,
                "./fy.js": 37071,
                "./ga": 41734,
                "./ga.js": 41734,
                "./gd": 70217,
                "./gd.js": 70217,
                "./gl": 77329,
                "./gl.js": 77329,
                "./gom-deva": 32124,
                "./gom-deva.js": 32124,
                "./gom-latn": 93383,
                "./gom-latn.js": 93383,
                "./gu": 95050,
                "./gu.js": 95050,
                "./he": 11713,
                "./he.js": 11713,
                "./hi": 43861,
                "./hi.js": 43861,
                "./hr": 26308,
                "./hr.js": 26308,
                "./hu": 90609,
                "./hu.js": 90609,
                "./hy-am": 17160,
                "./hy-am.js": 17160,
                "./id": 74063,
                "./id.js": 74063,
                "./is": 89374,
                "./is.js": 89374,
                "./it": 88383,
                "./it-ch": 21827,
                "./it-ch.js": 21827,
                "./it.js": 88383,
                "./ja": 23827,
                "./ja.js": 23827,
                "./jv": 89722,
                "./jv.js": 89722,
                "./ka": 41794,
                "./ka.js": 41794,
                "./kk": 27088,
                "./kk.js": 27088,
                "./km": 96870,
                "./km.js": 96870,
                "./kn": 84451,
                "./kn.js": 84451,
                "./ko": 63164,
                "./ko.js": 63164,
                "./ku": 98174,
                "./ku-kmr": 6181,
                "./ku-kmr.js": 6181,
                "./ku.js": 98174,
                "./ky": 78474,
                "./ky.js": 78474,
                "./lb": 79680,
                "./lb.js": 79680,
                "./lo": 15867,
                "./lo.js": 15867,
                "./lt": 45766,
                "./lt.js": 45766,
                "./lv": 69532,
                "./lv.js": 69532,
                "./me": 58076,
                "./me.js": 58076,
                "./mi": 41848,
                "./mi.js": 41848,
                "./mk": 30306,
                "./mk.js": 30306,
                "./ml": 73739,
                "./ml.js": 73739,
                "./mn": 99053,
                "./mn.js": 99053,
                "./mr": 86169,
                "./mr.js": 86169,
                "./ms": 73386,
                "./ms-my": 92297,
                "./ms-my.js": 92297,
                "./ms.js": 73386,
                "./mt": 77075,
                "./mt.js": 77075,
                "./my": 72264,
                "./my.js": 72264,
                "./nb": 22274,
                "./nb.js": 22274,
                "./ne": 8235,
                "./ne.js": 8235,
                "./nl": 92572,
                "./nl-be": 43784,
                "./nl-be.js": 43784,
                "./nl.js": 92572,
                "./nn": 54566,
                "./nn.js": 54566,
                "./oc-lnc": 69330,
                "./oc-lnc.js": 69330,
                "./pa-in": 29849,
                "./pa-in.js": 29849,
                "./pl": 94418,
                "./pl.js": 94418,
                "./pt": 79834,
                "./pt-br": 48303,
                "./pt-br.js": 48303,
                "./pt.js": 79834,
                "./ro": 24457,
                "./ro.js": 24457,
                "./ru": 82271,
                "./ru.js": 82271,
                "./sd": 1221,
                "./sd.js": 1221,
                "./se": 33478,
                "./se.js": 33478,
                "./si": 17538,
                "./si.js": 17538,
                "./sk": 5784,
                "./sk.js": 5784,
                "./sl": 46637,
                "./sl.js": 46637,
                "./sq": 86794,
                "./sq.js": 86794,
                "./sr": 45719,
                "./sr-cyrl": 3322,
                "./sr-cyrl.js": 3322,
                "./sr.js": 45719,
                "./ss": 56e3,
                "./ss.js": 56e3,
                "./sv": 41011,
                "./sv.js": 41011,
                "./sw": 40748,
                "./sw.js": 40748,
                "./ta": 11025,
                "./ta.js": 11025,
                "./te": 11885,
                "./te.js": 11885,
                "./tet": 28861,
                "./tet.js": 28861,
                "./tg": 86571,
                "./tg.js": 86571,
                "./th": 55802,
                "./th.js": 55802,
                "./tk": 59527,
                "./tk.js": 59527,
                "./tl-ph": 29231,
                "./tl-ph.js": 29231,
                "./tlh": 31052,
                "./tlh.js": 31052,
                "./tr": 85096,
                "./tr.js": 85096,
                "./tzl": 79846,
                "./tzl.js": 79846,
                "./tzm": 81765,
                "./tzm-latn": 97711,
                "./tzm-latn.js": 97711,
                "./tzm.js": 81765,
                "./ug-cn": 48414,
                "./ug-cn.js": 48414,
                "./uk": 16618,
                "./uk.js": 16618,
                "./ur": 57777,
                "./ur.js": 57777,
                "./uz": 57609,
                "./uz-latn": 72475,
                "./uz-latn.js": 72475,
                "./uz.js": 57609,
                "./vi": 21135,
                "./vi.js": 21135,
                "./x-pseudo": 64051,
                "./x-pseudo.js": 64051,
                "./yo": 82218,
                "./yo.js": 82218,
                "./zh-cn": 52648,
                "./zh-cn.js": 52648,
                "./zh-hk": 1632,
                "./zh-hk.js": 1632,
                "./zh-mo": 31541,
                "./zh-mo.js": 31541,
                "./zh-tw": 50304,
                "./zh-tw.js": 50304
            };
            function o(e) {
                var t = a(e);
                return n(t)
            }
            function a(e) {
                if (!n.o(r, e)) {
                    var t = new Error("Cannot find module '" + e + "'");
                    throw t.code = "MODULE_NOT_FOUND",
                    t
                }
                return r[e]
            }
            o.keys = function() {
                return Object.keys(r)
            }
            ,
            o.resolve = a,
            e.exports = o,
            o.id = 35358
        }
        ,
        38352: (e, t, n) => {
            "use strict";
            var r, o, a;
            n.d(t, {
                CO: () => r,
                pu: () => a,
                u1: () => o
            }),
            function(e) {
                e[e.MainAccount = 1] = "MainAccount",
                e[e.ChildAccount = 2] = "ChildAccount"
            }(r || (r = {})),
            function(e) {
                e[e.Normal = 1] = "Normal",
                e[e.Ban = 2] = "Ban"
            }(o || (o = {})),
            function(e) {
                e[e.Normal = 1] = "Normal",
                e[e.Ban = 2] = "Ban"
            }(a || (a = {}))
        }
        ,
        45689: (e, t, n) => {
            "use strict";
            n.d(t, {
                b: () => i
            });
            var r = n(87781)
              , o = n(3204)
              , a = (0,
            r.Gk)(o.Jt, o.bE)
              , i = (a[0],
            a[1])
        }
        ,
        47145: (e, t, n) => {
            "use strict";
            n.d(t, {
                A: () => i
            });
            var r = n(96540);
            var o = n(39677)
              , a = function() {
                return a = Object.assign || function(e) {
                    for (var t, n = 1, r = arguments.length; n < r; n++)
                        for (var o in t = arguments[n])
                            Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
                    return e
                }
                ,
                a.apply(this, arguments)
            };
            const i = function(e) {
                var t = e.noImg
                  , n = e.title
                  , i = function(e, t) {
                    var n = {};
                    for (var r in e)
                        Object.prototype.hasOwnProperty.call(e, r) && t.indexOf(r) < 0 && (n[r] = e[r]);
                    if (null != e && "function" == typeof Object.getOwnPropertySymbols) {
                        var o = 0;
                        for (r = Object.getOwnPropertySymbols(e); o < r.length; o++)
                            t.indexOf(r[o]) < 0 && Object.prototype.propertyIsEnumerable.call(e, r[o]) && (n[r[o]] = e[r[o]])
                    }
                    return n
                }(e, ["noImg", "title"]);
                return r.createElement("div", {
                    className: "empty-placeholder_wrapper__FT31z"
                }, t ? n : r.createElement(o.default, a({
                    img: "https://bstatic.cdnfe.com/static/files/sc/13fb1edb-c0f3-44a5-8f42-3c7e7a4a11e2.png",
                    title: n,
                    height: 140
                }, i)))
            }
        }
        ,
        50477: () => {}
        ,
        50953: (e, t, n) => {
            "use strict";
            n.d(t, {
                S: () => s
            });
            var r, o = n(37171), a = n(25208), i = n(19026), l = n(87781), c = n(45689), u = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return u([(0,
                    l.kl)()], e.prototype, "rejectReason", void 0),
                    u([(0,
                    l.j4)()], e.prototype, "retrySettle", void 0),
                    e
                }();
                e.RejectInfo = t;
                var n = function() {
                    function e() {}
                    return u([(0,
                    l.aI)()], e.prototype, "mallId", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "entityId", void 0),
                    e
                }();
                e.InitInfo = n;
                var r = function() {
                    function e() {}
                    return u([(0,
                    o.X)(), (0,
                    a.Mx)({}, {
                        each: !0
                    })], e.prototype, "signedProtocolTypeList", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "submitEntityCheck", void 0),
                    u([(0,
                    l.j4)()], e.prototype, "inSendOverseasAuditGrey", void 0),
                    e
                }();
                e.EditInfo = r;
                var c = function() {
                    function e() {}
                    return u([(0,
                    o.X)(), (0,
                    a.Mx)({}, {
                        each: !0
                    })], e.prototype, "signedProtocolTypeList", void 0),
                    e
                }();
                e.ApprovedInfo = c;
                var s = function() {
                    function e() {}
                    return u([(0,
                    l.aI)()], e.prototype, "sellerFinderStrategy", void 0),
                    u([(0,
                    o.X)(), (0,
                    i.K)(), (0,
                    l.ZU)(function() {
                        return t
                    })], e.prototype, "rejectInfo", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "appealStatus", void 0),
                    u([(0,
                    o.X)(), (0,
                    i.K)(), (0,
                    l.ZU)(function() {
                        return n
                    })], e.prototype, "initInfo", void 0),
                    u([(0,
                    o.X)(), (0,
                    i.K)(), (0,
                    l.ZU)(function() {
                        return r
                    })], e.prototype, "editInfo", void 0),
                    u([(0,
                    o.X)(), (0,
                    i.K)(), (0,
                    l.ZU)(function() {
                        return c
                    })], e.prototype, "approvedInfo", void 0),
                    u([(0,
                    l.j4)()], e.prototype, "isSubAccount", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "mallId", void 0),
                    u([(0,
                    l.kl)()], e.prototype, "mallName", void 0),
                    u([(0,
                    l.aI)()], e.prototype, "entityRecordId", void 0),
                    u([(0,
                    l.j4)()], e.prototype, "canAppeal", void 0),
                    e
                }();
                e.Res = s;
                var d = function() {
                    function e() {}
                    return u([(0,
                    o.X)(), (0,
                    i.K)(), (0,
                    l.ZU)(function() {
                        return s
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = d
            }(r || (r = {}));
            var s = function(e, t) {
                return (0,
                c.b)(r.Response, "/moonKnight/api/mallQuery/querySellerFinderStrategyAfterLogin", e, t)
            }
        }
        ,
        51026: (e, t, n) => {
            "use strict";
            n.d(t, {
                r: () => o
            });
            var r = n(46220)
              , o = function(e) {
                return function(t) {
                    var n = t;
                    (null == n ? void 0 : n.noNeedToast) || (n.response && (n = n.response),
                    "[object Object]" === Object.prototype.toString.call(n) ? n.message ? r.default.warn(n.message || e || "服务器开小差", void 0, void 0, {
                        zIndex: 9999
                    }) : r.default.warn(n.error_msg || n.errorMsg || e || "服务器开小差", void 0, void 0, {
                        zIndex: 9999
                    }) : r.default.warn(n || e || "服务器开小差", void 0, void 0, {
                        zIndex: 9999
                    }))
                }
            }
        }
        ,
        54670: (e, t, n) => {
            "use strict";
            n.d(t, {
                n: () => f
            });
            var r, o = n(37171), a = n(36668), i = n(25208), l = n(1358), c = n(19026), u = n(87781), s = n(45689), d = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return d([(0,
                    u.aI)()], e.prototype, "id", void 0),
                    d([(0,
                    u.kl)()], e.prototype, "title", void 0),
                    d([(0,
                    u.kl)()], e.prototype, "content", void 0),
                    d([(0,
                    u.aI)()], e.prototype, "sendTime", void 0),
                    d([(0,
                    u.aI)()], e.prototype, "readStatus", void 0),
                    d([(0,
                    u.kl)()], e.prototype, "jumpUrl", void 0),
                    d([(0,
                    u.kl)()], e.prototype, "linkText", void 0),
                    d([(0,
                    u.aI)()], e.prototype, "msgActionType", void 0),
                    d([(0,
                    u.j4)()], e.prototype, "forceRead", void 0),
                    d([(0,
                    u.aI)()], e.prototype, "forceReadSecond", void 0),
                    d([(0,
                    u.j4)()], e.prototype, "isRichContent", void 0),
                    d([(0,
                    o.X)(), (0,
                    a.IP)({
                        each: !0
                    })], e.prototype, "fileUrl", void 0),
                    e
                }();
                e.DataListItem = t;
                var n = function() {
                    function e() {}
                    return d([(0,
                    i.Mx)()], e.prototype, "pageNo", void 0),
                    d([(0,
                    i.Mx)()], e.prototype, "totalPage", void 0),
                    d([(0,
                    i.Mx)()], e.prototype, "totalRecord", void 0),
                    d([(0,
                    i.Mx)()], e.prototype, "pageSize", void 0),
                    d([(0,
                    l.Wb)(), (0,
                    c.K)(), (0,
                    u.ZU)(function() {
                        return t
                    })], e.prototype, "dataList", void 0),
                    e
                }();
                e.Res = n;
                var r = function() {
                    function e() {}
                    return d([(0,
                    o.X)(), (0,
                    c.K)(), (0,
                    u.ZU)(function() {
                        return n
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = r
            }(r || (r = {}));
            var f = function(e, t) {
                return (0,
                s.b)(r.Response, "/bg/quick/api/kj/merchant/msgBox/page", e, t)
            }
        }
        ,
        61419: (e, t, n) => {
            "use strict";
            n.d(t, {
                B: () => i,
                A: () => l
            });
            var r = n(96540);
            var o = n(41534)
              , a = function() {
                return a = Object.assign || function(e) {
                    for (var t, n = 1, r = arguments.length; n < r; n++)
                        for (var o in t = arguments[n])
                            Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
                    return e
                }
                ,
                a.apply(this, arguments)
            }
              , i = function(e) {
                e.height;
                var t = function(e, t) {
                    var n = {};
                    for (var r in e)
                        Object.prototype.hasOwnProperty.call(e, r) && t.indexOf(r) < 0 && (n[r] = e[r]);
                    if (null != e && "function" == typeof Object.getOwnPropertySymbols) {
                        var o = 0;
                        for (r = Object.getOwnPropertySymbols(e); o < r.length; o++)
                            t.indexOf(r[o]) < 0 && Object.prototype.propertyIsEnumerable.call(e, r[o]) && (n[r[o]] = e[r[o]])
                    }
                    return n
                }(e, ["height"]);
                return r.createElement("div", {
                    className: "loading-card_container__GU92P",
                    style: c(e)
                }, r.createElement(o.default, a({
                    size: "large"
                }, t)))
            };
            const l = i;
            var c = function(e) {
                var t = e.style
                  , n = e.height
                  , r = e.width;
                return a(a({}, t), {
                    height: n,
                    width: r
                })
            }
        }
        ,
        73734: (e, t, n) => {
            "use strict";
            n.d(t, {
                OB: () => c,
                g8: () => u
            });
            var r = n(75657)
              , o = n(45269)
              , a = n(3204)
              , i = function(e, t, n, r) {
                return new (n || (n = Promise))(function(o, a) {
                    function i(e) {
                        try {
                            c(r.next(e))
                        } catch (e) {
                            a(e)
                        }
                    }
                    function l(e) {
                        try {
                            c(r.throw(e))
                        } catch (e) {
                            a(e)
                        }
                    }
                    function c(e) {
                        var t;
                        e.done ? o(e.value) : (t = e.value,
                        t instanceof n ? t : new n(function(e) {
                            e(t)
                        }
                        )).then(i, l)
                    }
                    c((r = r.apply(e, t || [])).next())
                }
                )
            }
              , l = function(e, t) {
                var n, r, o, a = {
                    label: 0,
                    sent: function() {
                        if (1 & o[0])
                            throw o[1];
                        return o[1]
                    },
                    trys: [],
                    ops: []
                }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                return i.next = l(0),
                i.throw = l(1),
                i.return = l(2),
                "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                    return this
                }
                ),
                i;
                function l(l) {
                    return function(c) {
                        return function(l) {
                            if (n)
                                throw new TypeError("Generator is already executing.");
                            for (; i && (i = 0,
                            l[0] && (a = 0)),
                            a; )
                                try {
                                    if (n = 1,
                                    r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                    0) : r.next) && !(o = o.call(r, l[1])).done)
                                        return o;
                                    switch (r = 0,
                                    o && (l = [2 & l[0], o.value]),
                                    l[0]) {
                                    case 0:
                                    case 1:
                                        o = l;
                                        break;
                                    case 4:
                                        return a.label++,
                                        {
                                            value: l[1],
                                            done: !1
                                        };
                                    case 5:
                                        a.label++,
                                        r = l[1],
                                        l = [0];
                                        continue;
                                    case 7:
                                        l = a.ops.pop(),
                                        a.trys.pop();
                                        continue;
                                    default:
                                        if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                            a = 0;
                                            continue
                                        }
                                        if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                            a.label = l[1];
                                            break
                                        }
                                        if (6 === l[0] && a.label < o[1]) {
                                            a.label = o[1],
                                            o = l;
                                            break
                                        }
                                        if (o && a.label < o[2]) {
                                            a.label = o[2],
                                            a.ops.push(l);
                                            break
                                        }
                                        o[2] && a.ops.pop(),
                                        a.trys.pop();
                                        continue
                                    }
                                    l = t.call(e, a)
                                } catch (e) {
                                    l = [6, e],
                                    r = 0
                                } finally {
                                    n = o = 0
                                }
                            if (5 & l[0])
                                throw l[1];
                            return {
                                value: l[0] ? l[1] : void 0,
                                done: !0
                            }
                        }([l, c])
                    }
                }
            };
            function c(e) {
                return i(this, arguments, void 0, function(e) {
                    var t = e.userId
                      , n = e.mallId;
                    return l(this, function(e) {
                        return (0,
                        o.xI)({
                            tkDomain: "sc",
                            bizSys: "sc",
                            platType: "pc",
                            dr: "SH",
                            isProd: (0,
                            r.KV)(),
                            mallId: n || void 0,
                            userId: t || void 0,
                            post: a.bE
                        }),
                        [2]
                    })
                })
            }
            function u() {
                return i(this, void 0, void 0, function() {
                    return l(this, function(e) {
                        return (0,
                        o.i_)({
                            pageSn: 14503,
                            op: "pv"
                        }),
                        [2]
                    })
                })
            }
        }
        ,
        78378: (e, t, n) => {
            "use strict";
            n.d(t, {
                A: () => a
            });
            var r = n(96540);
            var o = n(30191);
            const a = function(e) {
                var t = e.count
                  , n = e.children;
                return r.createElement("div", {
                    className: "badge-label_wrapper__pfJyn"
                }, r.createElement("div", null, n), r.createElement(o.default, {
                    count: t || 0
                }))
            }
        }
        ,
        81635: () => {}
        ,
        89257: (e, t, n) => {
            "use strict";
            n.d(t, {
                $: () => o
            });
            var r = n(46632)
              , o = function(e, t) {
                var n = (0,
                r.A)(e, t);
                return function() {
                    for (var e = [], r = 0; r < arguments.length; r++)
                        e[r] = arguments[r];
                    var o = n.apply(void 0, e);
                    return o instanceof Promise && o.catch(function(r) {
                        throw t && n.cache.delete(t.apply(void 0, e)),
                        r
                    }),
                    o
                }
            }
        }
        ,
        89664: (e, t, n) => {
            "use strict";
            n.d(t, {
                A: () => v
            });
            var r, o = n(50016), a = n(51026), i = n(37171), l = n(19026), c = n(87781), u = n(45689), s = function(e, t, n, r) {
                var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                    i = Reflect.decorate(e, t, n, r);
                else
                    for (var l = e.length - 1; l >= 0; l--)
                        (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                return a > 3 && i && Object.defineProperty(t, n, i),
                i
            };
            !function(e) {
                var t = function() {
                    function e() {}
                    return s([(0,
                    c.kl)()], e.prototype, "groupCode", void 0),
                    s([(0,
                    c.kl)()], e.prototype, "groupDesc", void 0),
                    s([(0,
                    c.aI)()], e.prototype, "unreadNum", void 0),
                    e
                }();
                e.ResItem = t;
                var n = function() {
                    function e() {}
                    return s([(0,
                    i.X)(), (0,
                    l.K)(), (0,
                    c.ZU)(function() {
                        return t
                    })], e.prototype, "res", void 0),
                    e
                }();
                e.Response = n,
                e.Res = []
            }(r || (r = {}));
            var d;
            !function(e) {
                e.Res = {};
                var t = function() {
                    function e() {}
                    return function(e, t, n, r) {
                        var o, a = arguments.length, i = a < 3 ? t : null === r ? r = Object.getOwnPropertyDescriptor(t, n) : r;
                        if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
                            i = Reflect.decorate(e, t, n, r);
                        else
                            for (var l = e.length - 1; l >= 0; l--)
                                (o = e[l]) && (i = (a < 3 ? o(i) : a > 3 ? o(t, n, i) : o(t, n)) || i);
                        a > 3 && i && Object.defineProperty(t, n, i)
                    }([(0,
                    i.X)()], e.prototype, "res", void 0),
                    e
                }();
                e.Response = t
            }(d || (d = {}));
            var f = n(95367)
              , p = (0,
            o.eU)([]);
            const v = function() {
                var e = (0,
                o.fp)(p)
                  , t = e[0]
                  , n = e[1]
                  , i = (0,
                o.fp)(f.lB)[1]
                  , l = function() {
                    return (e = {},
                    (0,
                    u.b)(r.Response, "/bg/quick/api/kj/merchant/msgBox/group/unreadNum", e, undefined)).then(function(e) {
                        var t = e || [];
                        return n(t),
                        i(t.reduce(function(e, t) {
                            return e + (t.unreadNum || 0)
                        }, 0)),
                        e
                    });
                    var e
                };
                return {
                    typeList: t,
                    readMsgAndRefreshRedDot: function(e) {
                        return t = void 0,
                        n = [e],
                        o = function(e) {
                            var t, n = e.msgId;
                            return function(e, t) {
                                var n, r, o, a = {
                                    label: 0,
                                    sent: function() {
                                        if (1 & o[0])
                                            throw o[1];
                                        return o[1]
                                    },
                                    trys: [],
                                    ops: []
                                }, i = Object.create(("function" == typeof Iterator ? Iterator : Object).prototype);
                                return i.next = l(0),
                                i.throw = l(1),
                                i.return = l(2),
                                "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                                    return this
                                }
                                ),
                                i;
                                function l(l) {
                                    return function(c) {
                                        return function(l) {
                                            if (n)
                                                throw new TypeError("Generator is already executing.");
                                            for (; i && (i = 0,
                                            l[0] && (a = 0)),
                                            a; )
                                                try {
                                                    if (n = 1,
                                                    r && (o = 2 & l[0] ? r.return : l[0] ? r.throw || ((o = r.return) && o.call(r),
                                                    0) : r.next) && !(o = o.call(r, l[1])).done)
                                                        return o;
                                                    switch (r = 0,
                                                    o && (l = [2 & l[0], o.value]),
                                                    l[0]) {
                                                    case 0:
                                                    case 1:
                                                        o = l;
                                                        break;
                                                    case 4:
                                                        return a.label++,
                                                        {
                                                            value: l[1],
                                                            done: !1
                                                        };
                                                    case 5:
                                                        a.label++,
                                                        r = l[1],
                                                        l = [0];
                                                        continue;
                                                    case 7:
                                                        l = a.ops.pop(),
                                                        a.trys.pop();
                                                        continue;
                                                    default:
                                                        if (!((o = (o = a.trys).length > 0 && o[o.length - 1]) || 6 !== l[0] && 2 !== l[0])) {
                                                            a = 0;
                                                            continue
                                                        }
                                                        if (3 === l[0] && (!o || l[1] > o[0] && l[1] < o[3])) {
                                                            a.label = l[1];
                                                            break
                                                        }
                                                        if (6 === l[0] && a.label < o[1]) {
                                                            a.label = o[1],
                                                            o = l;
                                                            break
                                                        }
                                                        if (o && a.label < o[2]) {
                                                            a.label = o[2],
                                                            a.ops.push(l);
                                                            break
                                                        }
                                                        o[2] && a.ops.pop(),
                                                        a.trys.pop();
                                                        continue
                                                    }
                                                    l = t.call(e, a)
                                                } catch (e) {
                                                    l = [6, e],
                                                    r = 0
                                                } finally {
                                                    n = o = 0
                                                }
                                            if (5 & l[0])
                                                throw l[1];
                                            return {
                                                value: l[0] ? l[1] : void 0,
                                                done: !0
                                            }
                                        }([l, c])
                                    }
                                }
                            }(this, function(e) {
                                switch (e.label) {
                                case 0:
                                    return e.trys.push([0, 2, , 3]),
                                    [4, (r = {
                                        msgId: n
                                    },
                                    (0,
                                    u.b)(d.Response, "/bg/quick/api/kj/merchant/msgBox/read", r, void 0))];
                                case 1:
                                    return e.sent(),
                                    l(),
                                    [3, 3];
                                case 2:
                                    return t = e.sent(),
                                    (0,
                                    a.r)()(t),
                                    [3, 3];
                                case 3:
                                    return [2]
                                }
                                var r
                            })
                        }
                        ,
                        new ((r = void 0) || (r = Promise))(function(e, a) {
                            function i(e) {
                                try {
                                    c(o.next(e))
                                } catch (e) {
                                    a(e)
                                }
                            }
                            function l(e) {
                                try {
                                    c(o.throw(e))
                                } catch (e) {
                                    a(e)
                                }
                            }
                            function c(t) {
                                var n;
                                t.done ? e(t.value) : (n = t.value,
                                n instanceof r ? n : new r(function(e) {
                                    e(n)
                                }
                                )).then(i, l)
                            }
                            c((o = o.apply(t, n || [])).next())
                        }
                        );
                        var t, n, r, o
                    },
                    getMessageTypeListAndUnreadNum: l
                }
            }
        }
        ,
        93865: (e, t, n) => {
            "use strict";
            n.d(t, {
                A: () => r
            });
            const r = {
                card: "msg-brief-card_card__3mU9t",
                header: "msg-brief-card_header__MFqee",
                unreadHeader: "msg-brief-card_unreadHeader__4SxSj",
                body: "msg-brief-card_body__dij1S",
                unreadBody: "msg-brief-card_unreadBody__TEeRY",
                footer: "msg-brief-card_footer__flnoX",
                readStatusDot: "msg-brief-card_readStatusDot__GRwn0",
                default: "msg-brief-card_default__3Gikr",
                unreadDot: "msg-brief-card_unreadDot__vFQxA",
                content: "msg-brief-card_content__H6KwT",
                slimHeader: "msg-brief-card_slimHeader__BSPjx",
                title: "msg-brief-card_title__-H0Y+",
                time: "msg-brief-card_time__RWzqt",
                slimBody: "msg-brief-card_slimBody__Qa+lf",
                slimFooter: "msg-brief-card_slimFooter__7IGjm",
                focus: "msg-brief-card_focus__qVORS",
                slimCard: "msg-brief-card_slimCard__ph90b"
            }
        }
        ,
        95367: (e, t, n) => {
            "use strict";
            n.d(t, {
                lB: () => a
            });
            var r = n(50016)
              , o = (0,
            r.eU)(Number.MIN_SAFE_INTEGER)
              , a = ((0,
            r.eU)(function(e) {
                return e(o)
            }),
            (0,
            r.eU)(0))
        }
        ,
        98959: (e, t, n) => {
            "use strict";
            n.d(t, {
                P: () => o
            });
            var r = n(8646)
              , o = function(e) {
                r.A.init({
                    getUid: function() {
                        return e
                    },
                    app: "bgb-sc-settle"
                })
            }
        }
    });
