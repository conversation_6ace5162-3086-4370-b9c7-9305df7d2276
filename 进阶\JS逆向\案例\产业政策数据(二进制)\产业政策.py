import requests
import execjs
import base64
class dsjpt():

    def __init__(self):
        self.url = 'http://www.spolicy.com/info_api/policyType/showPolicyType'
        self.headers = {
            'Content-Type':'application/octet-stream',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
        }
        self.js = execjs.compile(open('产业政策.js', encoding='utf-8').read())

    def get_data(self):
        data = {
            "policyType": "3",
            "province": "",
            "city": "",
            "downtown": "",
            "garden": "",
            "centralId": "",
            "sort": 0,
            "homePageFlag": 1,
            "pageNum": 1,
            "pageSize": 7
        }
        # 直接转bytes数据类型也可以

        data1 =self.js.call('PolicyInfoByTypeIdParam$encode', data)
        print(data1)
        res = requests.post(self.url, headers=self.headers, data=bytes(data1['data']))
        print(res)
        print(res.text)

    def main(self):
        self.get_data()

if __name__ == '__main__':
    dsj = dsjpt()
    dsj.main()