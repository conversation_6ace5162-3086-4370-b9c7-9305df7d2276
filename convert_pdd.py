#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDD.js webpack扣取转换脚本
将webpack打包文件转换为可手动调用的形式
"""

import re
import os

def convert_pdd_webpack(input_file, output_file):
    """
    转换PDD.js为可扣取的webpack形式
    """
    print(f"正在转换文件: {input_file}")
    
    try:
        # 读取原始文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"原始文件大小: {len(content)} 字符")
        
        # 1. 查找IIFE开始位置
        iife_start = content.find('( () => {')
        if iife_start == -1:
            print("错误: 未找到webpack IIFE结构")
            return False
        
        # 2. 查找模块对象定义
        modules_pattern = r'var\s+e,\s*t,\s*n,\s*r,\s*o,\s*a\s*=\s*\{'
        match = re.search(modules_pattern, content)
        
        if not match:
            print("错误: 未找到模块对象定义")
            return False
        
        # 找到模块对象的开始位置
        modules_start = match.end() - 1  # 包含 {
        
        # 3. 找到模块对象的结束位置
        brace_count = 0
        modules_end = -1
        in_string = False
        string_char = None
        escape_next = False
        
        for i in range(modules_start, len(content)):
            char = content[i]
            
            if escape_next:
                escape_next = False
                continue
                
            if char == '\\':
                escape_next = True
                continue
                
            if not in_string and (char == '"' or char == "'"):
                in_string = True
                string_char = char
                continue
                
            if in_string and char == string_char:
                in_string = False
                string_char = None
                continue
                
            if not in_string:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        modules_end = i
                        break
        
        if modules_end == -1:
            print("错误: 未找到模块对象结束位置")
            return False
        
        # 4. 提取模块定义
        modules_definition = content[modules_start:modules_end + 1]
        print(f"提取到模块定义，大小: {len(modules_definition)} 字符")
        
        # 5. 查找webpack运行时代码的结束位置
        # 寻找最后的自执行部分
        runtime_end_pattern = r'}\s*\)\(\);?\s*$'
        runtime_match = re.search(runtime_end_pattern, content)
        
        if not runtime_match:
            print("错误: 未找到webpack运行时结束位置")
            return False
        
        # 6. 构建新的文件内容
        header = """window = global

var bc;

"""
        
        # 从医保.js复制的webpack运行时代码模板
        webpack_runtime_template = """!function (e) {
    var t, n, r, o, a = {}, i = {};
    
    function l(t) {
        var n = i[t];
        if (void 0 !== n)
            return n.exports;
        var r = i[t] = {
            id: t,
            loaded: !1,
            exports: {}
        };
        return e[t].call(r.exports, r, r.exports, l),
        r.loaded = !0,
        r.exports
    }
    
    l.m = e,
    l.amdD = function() {
        throw new Error("define cannot be used indirect")
    },
    
    // 添加webpack的其他运行时方法
    l.d = function(e, t, n) {
        l.o(e, t) || Object.defineProperty(e, t, {
            enumerable: !0,
            get: n
        })
    },
    
    l.r = function(e) {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
            value: "Module"
        }),
        Object.defineProperty(e, "__esModule", {
            value: !0
        })
    },
    
    l.o = function(e, t) {
        return Object.prototype.hasOwnProperty.call(e, t)
    },
    
    l.n = function(e) {
        var t = e && e.__esModule ? function() {
            return e.default
        } : function() {
            return e
        };
        return l.d(t, "a", t), t
    };
    
    // 注释掉自动执行，暴露require函数
    // l(l.s = 16297);
    bc = l;
}"""
        
        # 7. 组装最终内容
        final_content = header + webpack_runtime_template + "(" + modules_definition + ");"
        
        # 8. 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"转换成功! 输出文件: {output_file}")
        print(f"输出文件大小: {len(final_content)} 字符")
        
        # 9. 提供使用说明
        print("\n=== 使用说明 ===")
        print("1. 加载转换后的文件: require('./PDD_extracted.js')")
        print("2. 检查bc变量: console.log(typeof bc)")
        print("3. 调用入口模块: bc(16297)")
        print("4. 调用核心模块: bc(3204)")
        print("5. 调用其他模块: bc(模块ID)")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

if __name__ == "__main__":
    input_file = "进阶/JS逆向/案例/拼多多跨境/PDD.js"
    output_file = "PDD_extracted.js"
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
    else:
        convert_pdd_webpack(input_file, output_file)
